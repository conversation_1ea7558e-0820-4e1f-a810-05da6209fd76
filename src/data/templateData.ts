// Template data for Contract and Quotation Builders

export interface Template {
  id: string;
  name: string;
  description: string;
  category: string;
  features: string[];
  color: string;
}

export const contractTemplates: Template[] = [
  { 
    id: "modern-construction", 
    name: "Modern Construction Contract", 
    description: "Interactive contract with digital signatures and real-time updates",
    category: "Modern",
    features: ["Digital Signatures", "Real-time Updates", "Mobile Responsive", "Progress Tracking"],
    color: "bg-blue-500"
  },
  { 
    id: "classic-professional", 
    name: "Classic Professional Contract", 
    description: "Traditional formal contract with comprehensive legal structure",
    category: "Classic",
    features: ["Legal Compliance", "Detailed Terms", "Professional Layout", "Print Ready"],
    color: "bg-emerald-500"
  },
  { 
    id: "residential-renovation", 
    name: "Residential Renovation Contract", 
    description: "Specialized contract for home renovation and remodeling projects",
    category: "Residential",
    features: ["Room-by-room Scope", "Material Specifications", "Timeline Tracking", "Warranty Terms"],
    color: "bg-purple-500"
  },
  { 
    id: "commercial-construction", 
    name: "Commercial Construction Contract", 
    description: "Comprehensive contract for commercial building projects",
    category: "Commercial",
    features: ["Multi-phase Planning", "Compliance Tracking", "Risk Management", "Subcontractor Terms"],
    color: "bg-orange-500"
  },
  { 
    id: "emergency-repair", 
    name: "Emergency Repair Contract", 
    description: "Fast-track contract for urgent repair and restoration work",
    category: "Emergency",
    features: ["Expedited Process", "24/7 Support", "Insurance Claims", "Priority Scheduling"],
    color: "bg-red-500"
  },
  { 
    id: "maintenance-service", 
    name: "Maintenance Service Contract", 
    description: "Ongoing maintenance and service agreement for regular upkeep",
    category: "Maintenance",
    features: ["Recurring Services", "Preventive Care", "Service Schedules", "Performance Metrics"],
    color: "bg-indigo-500"
  }
];

export const quotationTemplates: Template[] = [
  { 
    id: "modern-pricing", 
    name: "Modern Pricing Quotation", 
    description: "Interactive quotation with dynamic pricing and visual elements",
    category: "Modern",
    features: ["Dynamic Pricing", "Real-time Calculations", "Visual Charts", "Client Approval"],
    color: "bg-blue-500"
  },
  { 
    id: "classic-detailed", 
    name: "Classic Detailed Quotation", 
    description: "Traditional comprehensive quotation with detailed cost breakdown",
    category: "Classic",
    features: ["Detailed Breakdown", "Professional Format", "Tax Calculations", "Terms Included"],
    color: "bg-emerald-500"
  },
  { 
    id: "residential-quote", 
    name: "Residential Project Quote", 
    description: "Specialized quotation for residential construction and renovation",
    category: "Residential",
    features: ["Room-by-room Pricing", "Material Specs", "Timeline Included", "Warranty Terms"],
    color: "bg-purple-500"
  },
  { 
    id: "commercial-estimate", 
    name: "Commercial Project Estimate", 
    description: "Comprehensive quotation for commercial construction projects",
    category: "Commercial",
    features: ["Multi-phase Pricing", "Risk Assessment", "Compliance Notes", "Professional Presentation"],
    color: "bg-orange-500"
  },
  { 
    id: "quick-estimate", 
    name: "Quick Estimate Template", 
    description: "Fast and simple quotation for small projects and consultations",
    category: "Quick",
    features: ["Simplified Format", "Fast Generation", "Basic Pricing", "Instant Delivery"],
    color: "bg-green-500"
  },
  { 
    id: "detailed-analysis", 
    name: "Detailed Cost Analysis", 
    description: "Comprehensive quotation with extensive cost analysis and projections",
    category: "Detailed",
    features: ["Cost Analysis", "Material Breakdown", "Labor Details", "Risk Assessment"],
    color: "bg-purple-600"
  }
];

// Legacy WebTemplate interface for backward compatibility
export interface WebTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  features: string[];
  color: string;
  // Additional properties that might be used in old template files
  type?: string;
  preview?: string;
  customizable?: boolean;
}

// Export with legacy names for backward compatibility
export const contractWebTemplates: WebTemplate[] = contractTemplates;
export const quotationWebTemplates: WebTemplate[] = quotationTemplates;
