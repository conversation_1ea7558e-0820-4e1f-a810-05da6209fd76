import { ReactNode, useState } from "react";
import ContractSidebar from "./ContractSidebar";
import { cn } from "@/lib/utils";

interface ContractDashboardLayoutProps {
  children: ReactNode;
  className?: string;
}

const ContractDashboardLayout = ({ children, className }: ContractDashboardLayoutProps) => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Mobile Overlay */}
      {mobileMenuOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-30 lg:hidden"
          onClick={() => setMobileMenuOpen(false)}
        />
      )}

      {/* Sidebar */}
      <ContractSidebar
        className={cn(
          "fixed left-0 top-0 h-full z-40 transition-transform duration-300",
          "lg:translate-x-0", // Always visible on desktop
          mobileMenuOpen ? "translate-x-0" : "-translate-x-full lg:translate-x-0" // Hidden on mobile unless menu open
        )}
        onMobileMenuToggle={() => setMobileMenuOpen(!mobileMenuOpen)}
        mobileMenuOpen={mobileMenuOpen}
      />

      {/* Main Content */}
      <div className={cn(
        "flex-1 transition-all duration-300",
        "lg:ml-64", // Fixed margin on desktop
        "ml-0" // No margin on mobile
      )}>
        {/* Mobile Header */}
        <div className="lg:hidden bg-white border-b px-4 py-3 flex items-center justify-between">
          <button
            onClick={() => setMobileMenuOpen(true)}
            className="p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100"
          >
            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
          <h1 className="text-lg font-semibold text-primary">Contract Builder</h1>
          <div className="w-10" /> {/* Spacer for centering */}
        </div>

        <main className={cn("min-h-screen", className)}>
          {children}
        </main>
      </div>
    </div>
  );
};

export default ContractDashboardLayout;
