import { useState, useRef } from "react";
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, Ta<PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Palette,
  Upload,
  Download,
  Eye,
  Building2,
  User,
  Phone,
  Mail,
  MapPin,
  Save,
  RotateCcw,
  Image as ImageIcon,
  FileDown,
  Printer
} from "lucide-react";
import { downloadTemplate, generatePDF } from "@/utils/templateExport";

interface TemplateCustomization {
  primaryColor: string;
  secondaryColor: string;
  accentColor: string;
  backgroundColor: string;
  textColor: string;
  companyName: string;
  companyAddress: string;
  companyPhone: string;
  companyEmail: string;
  companyWebsite: string;
  logoUrl: string;
  logoFile: File | null;
}

interface TemplateCustomizationModalProps {
  isOpen: boolean;
  onClose: () => void;
  template: {
    id: string;
    name: string;
    description: string;
    category: string;
    type: string;
  } | null;
  onSaveCustomization: (templateId: string, customization: TemplateCustomization) => void;
}

const TemplateCustomizationModal = ({ 
  isOpen, 
  onClose, 
  template, 
  onSaveCustomization 
}: TemplateCustomizationModalProps) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const [customization, setCustomization] = useState<TemplateCustomization>({
    primaryColor: "#2563eb",
    secondaryColor: "#64748b",
    accentColor: "#f59e0b",
    backgroundColor: "#ffffff",
    textColor: "#1f2937",
    companyName: "",
    companyAddress: "",
    companyPhone: "",
    companyEmail: "",
    companyWebsite: "",
    logoUrl: "",
    logoFile: null
  });

  const [activeTab, setActiveTab] = useState("colors");

  // Preset color themes
  const colorThemes = [
    {
      name: "Professional Blue",
      primaryColor: "#2563eb",
      secondaryColor: "#64748b",
      accentColor: "#f59e0b",
      backgroundColor: "#ffffff",
      textColor: "#1f2937"
    },
    {
      name: "Modern Green",
      primaryColor: "#059669",
      secondaryColor: "#6b7280",
      accentColor: "#dc2626",
      backgroundColor: "#ffffff",
      textColor: "#111827"
    },
    {
      name: "Corporate Gray",
      primaryColor: "#374151",
      secondaryColor: "#9ca3af",
      accentColor: "#f59e0b",
      backgroundColor: "#ffffff",
      textColor: "#1f2937"
    },
    {
      name: "Creative Purple",
      primaryColor: "#7c3aed",
      secondaryColor: "#a78bfa",
      accentColor: "#f59e0b",
      backgroundColor: "#ffffff",
      textColor: "#1f2937"
    }
  ];

  if (!template) return null;

  const handleColorChange = (colorType: keyof TemplateCustomization, value: string) => {
    setCustomization(prev => ({
      ...prev,
      [colorType]: value
    }));
  };

  const handleInputChange = (field: keyof TemplateCustomization, value: string) => {
    setCustomization(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleLogoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const logoUrl = e.target?.result as string;
        setCustomization(prev => ({
          ...prev,
          logoFile: file,
          logoUrl: logoUrl
        }));
      };
      reader.readAsDataURL(file);
    }
  };

  const resetCustomization = () => {
    setCustomization({
      primaryColor: "#2563eb",
      secondaryColor: "#64748b",
      accentColor: "#f59e0b",
      backgroundColor: "#ffffff",
      textColor: "#1f2937",
      companyName: "",
      companyAddress: "",
      companyPhone: "",
      companyEmail: "",
      companyWebsite: "",
      logoUrl: "",
      logoFile: null
    });
  };

  const applyColorTheme = (theme: typeof colorThemes[0]) => {
    setCustomization(prev => ({
      ...prev,
      primaryColor: theme.primaryColor,
      secondaryColor: theme.secondaryColor,
      accentColor: theme.accentColor,
      backgroundColor: theme.backgroundColor,
      textColor: theme.textColor
    }));
  };

  const handleSave = () => {
    onSaveCustomization(template.id, customization);
    onClose();
  };

  const handleDownloadHTML = () => {
    const isContract = template.category === "contract" || template.name.toLowerCase().includes("contract");
    downloadTemplate(template, customization, isContract);
  };

  const handleGeneratePDF = () => {
    const isContract = template.category === "contract" || template.name.toLowerCase().includes("contract");
    generatePDF(template, customization, isContract);
  };

  const getPreviewContent = () => {
    const style = {
      backgroundColor: customization.backgroundColor,
      color: customization.textColor,
      borderColor: customization.primaryColor
    };

    const headerStyle = {
      backgroundColor: customization.primaryColor,
      color: customization.backgroundColor
    };

    const accentStyle = {
      color: customization.accentColor
    };

    return (
      <div className="bg-white p-6 border rounded-lg" style={style}>
        {/* Header with company branding */}
        <div className="flex items-center justify-between mb-6 p-4 rounded-lg" style={headerStyle}>
          <div className="flex items-center space-x-4">
            {customization.logoUrl && (
              <img 
                src={customization.logoUrl} 
                alt="Company Logo" 
                className="h-12 w-12 object-contain bg-white rounded p-1"
              />
            )}
            <div>
              <h1 className="text-xl font-bold">
                {customization.companyName || "Your Company Name"}
              </h1>
              <p className="text-sm opacity-90">
                {template.category === "contract" ? "Construction Contract" : "Project Quotation"}
              </p>
            </div>
          </div>
          <div className="text-right text-sm">
            <p>{customization.companyPhone || "(*************"}</p>
            <p>{customization.companyEmail || "<EMAIL>"}</p>
          </div>
        </div>

        {/* Company Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div>
            <h3 className="font-semibold mb-2" style={accentStyle}>Company Information</h3>
            <div className="space-y-1 text-sm">
              <p>{customization.companyName || "Your Company Name"}</p>
              <p>{customization.companyAddress || "123 Business Street"}</p>
              <p>{customization.companyPhone || "(*************"}</p>
              <p>{customization.companyEmail || "<EMAIL>"}</p>
              {customization.companyWebsite && <p>{customization.companyWebsite}</p>}
            </div>
          </div>
          <div>
            <h3 className="font-semibold mb-2" style={accentStyle}>Client Information</h3>
            <div className="space-y-1 text-sm">
              <p>John & Jane Smith</p>
              <p>456 Client Avenue</p>
              <p>(*************</p>
              <p><EMAIL></p>
            </div>
          </div>
        </div>

        {/* Project Details */}
        <div className="border-t pt-4" style={{ borderColor: customization.secondaryColor }}>
          <h3 className="font-semibold mb-2" style={accentStyle}>Project Details</h3>
          <p className="text-sm mb-4">
            {template.category === "contract" 
              ? "Construction of a residential home including foundation, framing, electrical, plumbing, and finishing work."
              : "Detailed quotation for residential construction project including materials, labor, and timeline estimates."
            }
          </p>
          
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="space-y-1">
              <div className="flex justify-between">
                <span>Materials:</span>
                <span>$125,000</span>
              </div>
              <div className="flex justify-between">
                <span>Labor:</span>
                <span>$85,000</span>
              </div>
            </div>
            <div className="space-y-1">
              <div className="flex justify-between">
                <span>Equipment:</span>
                <span>$15,000</span>
              </div>
              <div className="flex justify-between font-semibold border-t pt-1" style={{ borderColor: customization.secondaryColor }}>
                <span>Total:</span>
                <span style={accentStyle}>$225,000</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-7xl max-h-[95vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Palette className="h-5 w-5" />
            <span>Customize Template: {template.name}</span>
          </DialogTitle>
          <DialogDescription>
            Personalize your template with custom colors, company branding, and logo
          </DialogDescription>
        </DialogHeader>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Customization Controls */}
          <div className="space-y-6">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="colors">Colors & Theme</TabsTrigger>
                <TabsTrigger value="branding">Company Branding</TabsTrigger>
              </TabsList>
              
              <TabsContent value="colors" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Preset Color Themes</CardTitle>
                    <CardDescription>Quick start with professional color combinations</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 gap-3">
                      {colorThemes.map((theme, index) => (
                        <Button
                          key={index}
                          variant="outline"
                          className="h-auto p-3 flex flex-col items-start"
                          onClick={() => applyColorTheme(theme)}
                        >
                          <div className="flex items-center space-x-2 mb-2">
                            <div
                              className="w-4 h-4 rounded-full"
                              style={{ backgroundColor: theme.primaryColor }}
                            />
                            <div
                              className="w-4 h-4 rounded-full"
                              style={{ backgroundColor: theme.secondaryColor }}
                            />
                            <div
                              className="w-4 h-4 rounded-full"
                              style={{ backgroundColor: theme.accentColor }}
                            />
                          </div>
                          <span className="text-sm font-medium">{theme.name}</span>
                        </Button>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Custom Colors</CardTitle>
                    <CardDescription>Fine-tune colors to match your exact brand</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="primaryColor">Primary Color</Label>
                        <div className="flex items-center space-x-2">
                          <input
                            type="color"
                            id="primaryColor"
                            value={customization.primaryColor}
                            onChange={(e) => handleColorChange("primaryColor", e.target.value)}
                            className="w-12 h-10 border rounded cursor-pointer"
                          />
                          <Input
                            value={customization.primaryColor}
                            onChange={(e) => handleColorChange("primaryColor", e.target.value)}
                            placeholder="#2563eb"
                          />
                        </div>
                      </div>
                      
                      <div>
                        <Label htmlFor="secondaryColor">Secondary Color</Label>
                        <div className="flex items-center space-x-2">
                          <input
                            type="color"
                            id="secondaryColor"
                            value={customization.secondaryColor}
                            onChange={(e) => handleColorChange("secondaryColor", e.target.value)}
                            className="w-12 h-10 border rounded cursor-pointer"
                          />
                          <Input
                            value={customization.secondaryColor}
                            onChange={(e) => handleColorChange("secondaryColor", e.target.value)}
                            placeholder="#64748b"
                          />
                        </div>
                      </div>
                      
                      <div>
                        <Label htmlFor="accentColor">Accent Color</Label>
                        <div className="flex items-center space-x-2">
                          <input
                            type="color"
                            id="accentColor"
                            value={customization.accentColor}
                            onChange={(e) => handleColorChange("accentColor", e.target.value)}
                            className="w-12 h-10 border rounded cursor-pointer"
                          />
                          <Input
                            value={customization.accentColor}
                            onChange={(e) => handleColorChange("accentColor", e.target.value)}
                            placeholder="#f59e0b"
                          />
                        </div>
                      </div>
                      
                      <div>
                        <Label htmlFor="backgroundColor">Background</Label>
                        <div className="flex items-center space-x-2">
                          <input
                            type="color"
                            id="backgroundColor"
                            value={customization.backgroundColor}
                            onChange={(e) => handleColorChange("backgroundColor", e.target.value)}
                            className="w-12 h-10 border rounded cursor-pointer"
                          />
                          <Input
                            value={customization.backgroundColor}
                            onChange={(e) => handleColorChange("backgroundColor", e.target.value)}
                            placeholder="#ffffff"
                          />
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
              
              <TabsContent value="branding" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Company Information</CardTitle>
                    <CardDescription>Add your company details and logo</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="companyName">Company Name</Label>
                      <Input
                        id="companyName"
                        value={customization.companyName}
                        onChange={(e) => handleInputChange("companyName", e.target.value)}
                        placeholder="Your Company Name"
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor="companyAddress">Address</Label>
                      <Input
                        id="companyAddress"
                        value={customization.companyAddress}
                        onChange={(e) => handleInputChange("companyAddress", e.target.value)}
                        placeholder="123 Business Street, City, State 12345"
                      />
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="companyPhone">Phone</Label>
                        <Input
                          id="companyPhone"
                          value={customization.companyPhone}
                          onChange={(e) => handleInputChange("companyPhone", e.target.value)}
                          placeholder="(*************"
                        />
                      </div>
                      <div>
                        <Label htmlFor="companyEmail">Email</Label>
                        <Input
                          id="companyEmail"
                          value={customization.companyEmail}
                          onChange={(e) => handleInputChange("companyEmail", e.target.value)}
                          placeholder="<EMAIL>"
                        />
                      </div>
                    </div>
                    
                    <div>
                      <Label htmlFor="companyWebsite">Website (Optional)</Label>
                      <Input
                        id="companyWebsite"
                        value={customization.companyWebsite}
                        onChange={(e) => handleInputChange("companyWebsite", e.target.value)}
                        placeholder="www.company.com"
                      />
                    </div>
                    
                    <div>
                      <Label>Company Logo</Label>
                      <div className="mt-2">
                        {customization.logoUrl ? (
                          <div className="flex items-center space-x-4">
                            <img 
                              src={customization.logoUrl} 
                              alt="Company Logo" 
                              className="h-16 w-16 object-contain border rounded"
                            />
                            <Button
                              variant="outline"
                              onClick={() => fileInputRef.current?.click()}
                            >
                              <Upload className="h-4 w-4 mr-2" />
                              Change Logo
                            </Button>
                          </div>
                        ) : (
                          <Button
                            variant="outline"
                            onClick={() => fileInputRef.current?.click()}
                            className="w-full h-24 border-dashed"
                          >
                            <div className="text-center">
                              <ImageIcon className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
                              <p className="text-sm text-muted-foreground">Click to upload logo</p>
                            </div>
                          </Button>
                        )}
                        <input
                          ref={fileInputRef}
                          type="file"
                          accept="image/*"
                          onChange={handleLogoUpload}
                          className="hidden"
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>

          {/* Live Preview */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Live Preview</h3>
              <Button variant="outline" size="sm" onClick={resetCustomization}>
                <RotateCcw className="h-4 w-4 mr-2" />
                Reset
              </Button>
            </div>
            <div className="border rounded-lg p-4 bg-gray-50 max-h-96 overflow-y-auto">
              {getPreviewContent()}
            </div>
          </div>
        </div>
        
        <div className="flex justify-between items-center pt-6 border-t">
          <Badge variant="outline" className="text-sm">
            Template: {template.name}
          </Badge>
          <div className="flex justify-between items-center">
            <div className="flex space-x-2">
              <Button variant="outline" size="sm" onClick={handleDownloadHTML}>
                <FileDown className="h-4 w-4 mr-2" />
                Download HTML
              </Button>
              <Button variant="outline" size="sm" onClick={handleGeneratePDF}>
                <Printer className="h-4 w-4 mr-2" />
                Print/PDF
              </Button>
            </div>
            <div className="flex space-x-3">
              <Button variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button onClick={handleSave}>
                <Save className="h-4 w-4 mr-2" />
                Save & Use Template
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default TemplateCustomizationModal;
