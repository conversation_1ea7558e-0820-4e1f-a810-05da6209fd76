import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { 
  Building2, 
  User, 
  Phone, 
  Mail, 
  MapPin, 
  Calendar,
  DollarSign,
  FileText,
  CheckCircle,
  Clock,
  Signature
} from 'lucide-react';

interface ContractFormProps {
  contractId: string;
  template: any;
  customization: any;
  isReadOnly?: boolean;
}

interface ContractData {
  clientName: string;
  clientEmail: string;
  clientPhone: string;
  clientAddress: string;
  projectTitle: string;
  projectDescription: string;
  projectAddress: string;
  startDate: string;
  endDate: string;
  totalAmount: string;
  paymentTerms: string;
  materials: string;
  labor: string;
  permits: string;
  additionalTerms: string;
  clientSignature: string;
  contractorSignature: string;
  signedDate: string;
  status: 'draft' | 'sent' | 'signed' | 'completed';
}

export const ContractForm: React.FC<ContractFormProps> = ({
  contractId,
  template,
  customization,
  isReadOnly = false
}) => {
  const [contractData, setContractData] = useState<ContractData>({
    clientName: '',
    clientEmail: '',
    clientPhone: '',
    clientAddress: '',
    projectTitle: '',
    projectDescription: '',
    projectAddress: '',
    startDate: '',
    endDate: '',
    totalAmount: '',
    paymentTerms: '20% down payment, 30% at foundation completion, 30% at framing completion, 20% upon final completion',
    materials: '',
    labor: '',
    permits: '',
    additionalTerms: '',
    clientSignature: '',
    contractorSignature: '',
    signedDate: '',
    status: 'draft'
  });

  const [isSigning, setIsSigning] = useState(false);

  const handleInputChange = (field: keyof ContractData, value: string) => {
    if (isReadOnly) return;
    setContractData(prev => ({ ...prev, [field]: value }));
  };

  const handleSign = async () => {
    setIsSigning(true);
    // Simulate signing process
    setTimeout(() => {
      setContractData(prev => ({
        ...prev,
        clientSignature: prev.clientName,
        signedDate: new Date().toISOString().split('T')[0],
        status: 'signed'
      }));
      setIsSigning(false);
    }, 2000);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft': return 'bg-gray-100 text-gray-800';
      case 'sent': return 'bg-blue-100 text-blue-800';
      case 'signed': return 'bg-green-100 text-green-800';
      case 'completed': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        {/* Header */}
        <div 
          className="rounded-lg p-6 mb-6 text-white"
          style={{ backgroundColor: customization.primaryColor }}
        >
          <div className="flex justify-between items-start">
            <div className="flex items-center space-x-4">
              {customization.logoUrl ? (
                <img 
                  src={customization.logoUrl} 
                  alt="Company Logo" 
                  className="w-16 h-16 bg-white rounded-lg p-2 object-contain"
                />
              ) : (
                <div 
                  className="w-16 h-16 bg-white rounded-lg flex items-center justify-center text-lg font-bold"
                  style={{ color: customization.primaryColor }}
                >
                  {(customization.companyName || "CO").substring(0, 2).toUpperCase()}
                </div>
              )}
              <div>
                <h1 className="text-2xl font-bold">{customization.companyName || "Your Company"}</h1>
                <p className="opacity-90">Construction Contract</p>
                <p className="text-sm opacity-75">Template: {template.name}</p>
              </div>
            </div>
            <div className="text-right text-sm">
              <p>{customization.companyPhone || "(*************"}</p>
              <p>{customization.companyEmail || "<EMAIL>"}</p>
              {customization.companyWebsite && <p>{customization.companyWebsite}</p>}
            </div>
          </div>
        </div>

        {/* Status Badge */}
        <div className="mb-6">
          <Badge className={`${getStatusColor(contractData.status)} text-sm px-3 py-1`}>
            {contractData.status === 'draft' && <Clock className="w-4 h-4 mr-1" />}
            {contractData.status === 'sent' && <FileText className="w-4 h-4 mr-1" />}
            {contractData.status === 'signed' && <CheckCircle className="w-4 h-4 mr-1" />}
            {contractData.status === 'completed' && <CheckCircle className="w-4 h-4 mr-1" />}
            {contractData.status.charAt(0).toUpperCase() + contractData.status.slice(1)}
          </Badge>
        </div>

        {/* Client Information */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <User className="w-5 h-5" />
              <span>Client Information</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">Client Name *</label>
              <Input
                value={contractData.clientName}
                onChange={(e) => handleInputChange('clientName', e.target.value)}
                placeholder="Enter client name"
                disabled={isReadOnly}
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Email *</label>
              <Input
                type="email"
                value={contractData.clientEmail}
                onChange={(e) => handleInputChange('clientEmail', e.target.value)}
                placeholder="<EMAIL>"
                disabled={isReadOnly}
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Phone</label>
              <Input
                value={contractData.clientPhone}
                onChange={(e) => handleInputChange('clientPhone', e.target.value)}
                placeholder="(*************"
                disabled={isReadOnly}
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Address</label>
              <Input
                value={contractData.clientAddress}
                onChange={(e) => handleInputChange('clientAddress', e.target.value)}
                placeholder="Client address"
                disabled={isReadOnly}
              />
            </div>
          </CardContent>
        </Card>

        {/* Project Details */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Building2 className="w-5 h-5" />
              <span>Project Details</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-1">Project Title *</label>
              <Input
                value={contractData.projectTitle}
                onChange={(e) => handleInputChange('projectTitle', e.target.value)}
                placeholder="Enter project title"
                disabled={isReadOnly}
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Project Description *</label>
              <Textarea
                value={contractData.projectDescription}
                onChange={(e) => handleInputChange('projectDescription', e.target.value)}
                placeholder="Detailed description of the construction project..."
                rows={4}
                disabled={isReadOnly}
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Project Address</label>
              <Input
                value={contractData.projectAddress}
                onChange={(e) => handleInputChange('projectAddress', e.target.value)}
                placeholder="Project location address"
                disabled={isReadOnly}
              />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-1">Start Date</label>
                <Input
                  type="date"
                  value={contractData.startDate}
                  onChange={(e) => handleInputChange('startDate', e.target.value)}
                  disabled={isReadOnly}
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Expected Completion</label>
                <Input
                  type="date"
                  value={contractData.endDate}
                  onChange={(e) => handleInputChange('endDate', e.target.value)}
                  disabled={isReadOnly}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Contract Terms */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <DollarSign className="w-5 h-5" />
              <span>Contract Terms</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-1">Total Contract Amount *</label>
              <Input
                value={contractData.totalAmount}
                onChange={(e) => handleInputChange('totalAmount', e.target.value)}
                placeholder="$0.00"
                disabled={isReadOnly}
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Payment Terms</label>
              <Textarea
                value={contractData.paymentTerms}
                onChange={(e) => handleInputChange('paymentTerms', e.target.value)}
                rows={3}
                disabled={isReadOnly}
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Additional Terms & Conditions</label>
              <Textarea
                value={contractData.additionalTerms}
                onChange={(e) => handleInputChange('additionalTerms', e.target.value)}
                placeholder="Any additional terms, warranties, or conditions..."
                rows={4}
                disabled={isReadOnly}
              />
            </div>
          </CardContent>
        </Card>

        {/* Digital Signature */}
        {!isReadOnly && contractData.status !== 'signed' && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Signature className="w-5 h-5" />
                <span>Digital Signature</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <p className="text-gray-600 mb-4">
                  By clicking "Sign Contract", you agree to the terms and conditions outlined above.
                </p>
                <Button 
                  onClick={handleSign}
                  disabled={isSigning || !contractData.clientName || !contractData.clientEmail}
                  className="px-8 py-3"
                  style={{ backgroundColor: customization.primaryColor }}
                >
                  {isSigning ? (
                    <>
                      <Clock className="w-4 h-4 mr-2 animate-spin" />
                      Signing Contract...
                    </>
                  ) : (
                    <>
                      <Signature className="w-4 h-4 mr-2" />
                      Sign Contract
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Signed Contract Display */}
        {contractData.status === 'signed' && (
          <Card className="mb-6 border-green-200 bg-green-50">
            <CardContent className="pt-6">
              <div className="text-center">
                <CheckCircle className="w-12 h-12 text-green-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-green-800 mb-2">Contract Signed Successfully!</h3>
                <p className="text-green-700 mb-4">
                  This contract was digitally signed by {contractData.clientSignature} on {contractData.signedDate}
                </p>
                <div className="bg-white rounded-lg p-4 border border-green-200">
                  <p className="text-sm text-gray-600">Digital Signature:</p>
                  <p className="font-semibold text-lg">{contractData.clientSignature}</p>
                  <p className="text-sm text-gray-500">Signed on: {contractData.signedDate}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default ContractForm;
