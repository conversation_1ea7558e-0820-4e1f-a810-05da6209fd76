import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { 
  MessageCircle, 
  Send, 
  Clock, 
  AlertCircle, 
  CheckCircle, 
  User,
  Reply,
  Flag,
  Paperclip,
  X
} from "lucide-react";
import { ClientComment, ClientCommentResponse, createClientComment, addCommentResponse } from "@/types/project";

interface ClientCommentSystemProps {
  projectId: string;
  comments: ClientComment[];
  onAddComment: (comment: ClientComment) => void;
  onAddResponse: (commentId: string, response: ClientCommentResponse) => void;
  onUpdateCommentStatus: (commentId: string, status: ClientComment['status']) => void;
  isClientView?: boolean;
  currentUserName?: string;
  currentUserEmail?: string;
  currentUserId?: string;
}

const ClientCommentSystem = ({
  projectId,
  comments,
  onAddComment,
  onAddResponse,
  onUpdateCommentStatus,
  isClientView = false,
  currentUserName = "Anonymous",
  currentUserEmail = "",
  currentUserId = ""
}: ClientCommentSystemProps) => {
  const [newComment, setNewComment] = useState("");
  const [newCommentCategory, setNewCommentCategory] = useState<ClientComment['category']>("general");
  const [newCommentPriority, setNewCommentPriority] = useState<ClientComment['priority']>("medium");
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [replyContent, setReplyContent] = useState("");
  const [showCommentForm, setShowCommentForm] = useState(false);

  const handleSubmitComment = () => {
    if (!newComment.trim()) return;

    const comment = createClientComment(
      projectId,
      newComment,
      currentUserName,
      currentUserEmail,
      newCommentCategory,
      newCommentPriority
    );

    onAddComment(comment);
    setNewComment("");
    setNewCommentCategory("general");
    setNewCommentPriority("medium");
    setShowCommentForm(false);
  };

  const handleSubmitReply = (commentId: string) => {
    if (!replyContent.trim() || !currentUserId) return;

    const response: ClientCommentResponse = {
      id: `response_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      content: replyContent,
      timestamp: new Date().toISOString(),
      responderId: currentUserId,
      responderName: currentUserName,
      responderRole: isClientView ? 'contractor' : 'project_manager',
      isClientVisible: true
    };

    onAddResponse(commentId, response);
    setReplyContent("");
    setReplyingTo(null);
  };

  const getStatusColor = (status: ClientComment['status']) => {
    switch (status) {
      case 'new': return 'bg-red-500';
      case 'read': return 'bg-yellow-500';
      case 'responded': return 'bg-blue-500';
      case 'resolved': return 'bg-green-500';
      default: return 'bg-gray-500';
    }
  };

  const getPriorityColor = (priority: ClientComment['priority']) => {
    switch (priority) {
      case 'low': return 'bg-gray-500';
      case 'medium': return 'bg-yellow-500';
      case 'high': return 'bg-orange-500';
      default: return 'bg-gray-500';
    }
  };

  const getCategoryIcon = (category: ClientComment['category']) => {
    switch (category) {
      case 'concern': return <AlertCircle className="h-4 w-4" />;
      case 'question': return <MessageCircle className="h-4 w-4" />;
      case 'approval': return <CheckCircle className="h-4 w-4" />;
      case 'suggestion': return <Flag className="h-4 w-4" />;
      default: return <MessageCircle className="h-4 w-4" />;
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return "Just now";
    if (diffInHours < 24) return `${diffInHours}h ago`;
    if (diffInHours < 168) return `${Math.floor(diffInHours / 24)}d ago`;
    return date.toLocaleDateString();
  };

  const sortedComments = [...comments].sort((a, b) => 
    new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <MessageCircle className="h-5 w-5 text-primary" />
          <h3 className="text-lg font-semibold">
            {isClientView ? "Comments & Feedback" : "Client Comments"}
          </h3>
          {comments.length > 0 && (
            <Badge variant="outline">{comments.length}</Badge>
          )}
        </div>
        
        {isClientView && (
          <Button 
            onClick={() => setShowCommentForm(!showCommentForm)}
            size="sm"
          >
            <MessageCircle className="mr-2 h-4 w-4" />
            Add Comment
          </Button>
        )}
      </div>

      {/* New Comment Form */}
      {(showCommentForm || !isClientView) && isClientView && (
        <Card className="border-2 border-primary/20">
          <CardHeader>
            <CardTitle className="text-lg">Add New Comment</CardTitle>
            <CardDescription>
              Share your feedback, questions, or concerns about the project
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="category">Category</Label>
                <Select value={newCommentCategory} onValueChange={(value) => setNewCommentCategory(value as ClientComment['category'])}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="general">General Comment</SelectItem>
                    <SelectItem value="question">Question</SelectItem>
                    <SelectItem value="concern">Concern</SelectItem>
                    <SelectItem value="suggestion">Suggestion</SelectItem>
                    <SelectItem value="approval">Approval Request</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="priority">Priority</Label>
                <Select value={newCommentPriority} onValueChange={(value) => setNewCommentPriority(value as ClientComment['priority'])}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">Low</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="comment">Your Comment</Label>
              <Textarea
                id="comment"
                placeholder="Type your comment here..."
                value={newComment}
                onChange={(e) => setNewComment(e.target.value)}
                rows={4}
              />
            </div>
            
            <div className="flex justify-between items-center">
              <div className="text-sm text-muted-foreground">
                Your comment will be visible to the project team
              </div>
              <div className="flex space-x-2">
                <Button 
                  variant="outline" 
                  onClick={() => setShowCommentForm(false)}
                  size="sm"
                >
                  Cancel
                </Button>
                <Button 
                  onClick={handleSubmitComment}
                  disabled={!newComment.trim()}
                  size="sm"
                >
                  <Send className="mr-2 h-4 w-4" />
                  Post Comment
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Comments List */}
      {comments.length === 0 ? (
        <Card>
          <CardContent className="text-center py-8">
            <MessageCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">No comments yet</h3>
            <p className="text-muted-foreground">
              {isClientView 
                ? "Be the first to leave a comment or ask a question about this project."
                : "No client comments have been posted yet."
              }
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {sortedComments.map((comment) => (
            <Card key={comment.id} className="relative">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-3">
                    <Avatar className="h-8 w-8">
                      <AvatarFallback className="text-xs">
                        {comment.clientName.split(' ').map(n => n[0]).join('').toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="flex items-center space-x-2">
                        <span className="font-medium">{comment.clientName}</span>
                        <Badge variant="outline" className="text-xs">
                          {getCategoryIcon(comment.category)}
                          <span className="ml-1 capitalize">{comment.category}</span>
                        </Badge>
                      </div>
                      <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                        <Clock className="h-3 w-3" />
                        <span>{formatTimestamp(comment.timestamp)}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Badge className={`${getPriorityColor(comment.priority)} text-white text-xs`}>
                      {comment.priority}
                    </Badge>
                    <Badge className={`${getStatusColor(comment.status)} text-white text-xs`}>
                      {comment.status}
                    </Badge>
                  </div>
                </div>
              </CardHeader>
              
              <CardContent className="space-y-4">
                <p className="text-sm leading-relaxed">{comment.content}</p>
                
                {/* Responses */}
                {comment.responses && comment.responses.length > 0 && (
                  <div className="space-y-3 border-l-2 border-muted pl-4 ml-4">
                    {comment.responses.map((response) => (
                      <div key={response.id} className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <Avatar className="h-6 w-6">
                            <AvatarFallback className="text-xs">
                              {response.responderName.split(' ').map(n => n[0]).join('').toUpperCase()}
                            </AvatarFallback>
                          </Avatar>
                          <span className="text-sm font-medium">{response.responderName}</span>
                          <Badge variant="outline" className="text-xs">
                            {response.responderRole.replace('_', ' ')}
                          </Badge>
                          <span className="text-xs text-muted-foreground">
                            {formatTimestamp(response.timestamp)}
                          </span>
                        </div>
                        <p className="text-sm text-muted-foreground pl-8">{response.content}</p>
                      </div>
                    ))}
                  </div>
                )}
                
                {/* Reply Section */}
                {!isClientView && (
                  <div className="flex items-center space-x-2 pt-2 border-t">
                    {replyingTo === comment.id ? (
                      <div className="flex-1 space-y-2">
                        <Textarea
                          placeholder="Type your response..."
                          value={replyContent}
                          onChange={(e) => setReplyContent(e.target.value)}
                          rows={2}
                          className="text-sm"
                        />
                        <div className="flex space-x-2">
                          <Button 
                            size="sm" 
                            onClick={() => handleSubmitReply(comment.id)}
                            disabled={!replyContent.trim()}
                          >
                            <Send className="mr-2 h-3 w-3" />
                            Reply
                          </Button>
                          <Button 
                            variant="outline" 
                            size="sm" 
                            onClick={() => setReplyingTo(null)}
                          >
                            Cancel
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <>
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          onClick={() => setReplyingTo(comment.id)}
                        >
                          <Reply className="mr-2 h-3 w-3" />
                          Reply
                        </Button>
                        {comment.status !== 'resolved' && (
                          <Button 
                            variant="ghost" 
                            size="sm" 
                            onClick={() => onUpdateCommentStatus(comment.id, 'resolved')}
                          >
                            <CheckCircle className="mr-2 h-3 w-3" />
                            Mark Resolved
                          </Button>
                        )}
                      </>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default ClientCommentSystem;
