import { ReactNode } from "react";
import { Navigate } from "react-router-dom";
import { useQuotationAuth } from "@/contexts/QuotationAuthContext";

interface QuotationProtectedRouteProps {
  children: ReactNode;
}

const QuotationProtectedRoute = ({ children }: QuotationProtectedRouteProps) => {
  const { isAuthenticated, isLoading } = useQuotationAuth();

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-accent"></div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return <Navigate to="/quotation-login" replace />;
  }

  return <>{children}</>;
};

export default QuotationProtectedRoute;
