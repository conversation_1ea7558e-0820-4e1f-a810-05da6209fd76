
import { But<PERSON> } from "@/components/ui/button";
import { Menu, X, Building } from "lucide-react";
import { Link } from "react-router-dom";
import { useState } from "react";
import { useAuth } from "@/contexts/AuthContext";

const Navigation = () => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const { user } = useAuth();

  // Only show navigation for non-authenticated users (public pages)
  if (user) {
    return null;
  }
  
  return (
    <header className="border-b bg-white/80 backdrop-blur-sm sticky top-0 z-50">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          <Link to="/" className="flex items-center space-x-2">
            <div className="h-8 w-8 construction-gradient rounded-lg flex items-center justify-center">
              <Building className="h-5 w-5 text-white" />
            </div>
            <h1 className="text-xl md:text-2xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
              ConstructionSync
            </h1>
          </Link>
          
          {/* Public Navigation */}
          <nav className="hidden md:flex items-center space-x-4">
            <Button variant="ghost" asChild>
              <a href="#features">Features</a>
            </Button>
            <Button variant="ghost" asChild>
              <a href="#pricing">Pricing</a>
            </Button>
            <Button variant="ghost" asChild>
              <a href="#contact">Contact</a>
            </Button>
          </nav>
          
          <div className="flex items-center space-x-2">
            {/* Auth buttons for non-authenticated users */}
            <Button variant="outline" size="sm" asChild className="hidden sm:flex">
              <Link to="/login">Sign In</Link>
            </Button>
            <Button size="sm" asChild>
              <Link to="/register">Get Started</Link>
            </Button>

            {/* Mobile Menu Button */}
            <Button
              variant="outline"
              size="sm"
              className="sm:hidden"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            >
              {mobileMenuOpen ? <X className="h-4 w-4" /> : <Menu className="h-4 w-4" />}
            </Button>
          </div>
        </div>
        
        {/* Mobile Navigation */}
        {mobileMenuOpen && (
          <div className="sm:hidden mt-4 pb-4 border-t">
            <nav className="flex flex-col space-y-2 mt-4">
              <Button variant="ghost" asChild className="justify-start">
                <a href="#features" onClick={() => setMobileMenuOpen(false)}>
                  Features
                </a>
              </Button>
              <Button variant="ghost" asChild className="justify-start">
                <a href="#pricing" onClick={() => setMobileMenuOpen(false)}>
                  Pricing
                </a>
              </Button>
              <Button variant="ghost" asChild className="justify-start">
                <a href="#contact" onClick={() => setMobileMenuOpen(false)}>
                  Contact
                </a>
              </Button>
              <Button variant="outline" asChild className="justify-start">
                <Link to="/login" onClick={() => setMobileMenuOpen(false)}>
                  Sign In
                </Link>
              </Button>
              <Button asChild className="justify-start">
                <Link to="/register" onClick={() => setMobileMenuOpen(false)}>
                  Get Started
                </Link>
              </Button>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
};

export default Navigation;
