import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { 
  Building2, 
  User, 
  Phone, 
  Mail, 
  MapPin, 
  Calendar,
  DollarSign,
  FileText,
  CheckCircle,
  Clock,
  Signature,
  Plus,
  Trash2
} from 'lucide-react';

interface QuotationFormProps {
  quotationId: string;
  template: any;
  customization: any;
  isReadOnly?: boolean;
}

interface LineItem {
  id: string;
  description: string;
  quantity: number;
  unitPrice: number;
  total: number;
}

interface QuotationData {
  clientName: string;
  clientEmail: string;
  clientPhone: string;
  clientAddress: string;
  projectTitle: string;
  projectDescription: string;
  projectAddress: string;
  validUntil: string;
  lineItems: LineItem[];
  subtotal: number;
  taxRate: number;
  taxAmount: number;
  totalAmount: number;
  notes: string;
  terms: string;
  clientSignature: string;
  signedDate: string;
  status: 'draft' | 'sent' | 'accepted' | 'rejected' | 'expired';
}

export const QuotationForm: React.FC<QuotationFormProps> = ({
  quotationId,
  template,
  customization,
  isReadOnly = false
}) => {
  const [quotationData, setQuotationData] = useState<QuotationData>({
    clientName: '',
    clientEmail: '',
    clientPhone: '',
    clientAddress: '',
    projectTitle: '',
    projectDescription: '',
    projectAddress: '',
    validUntil: '',
    lineItems: [
      { id: '1', description: 'Materials & Supplies', quantity: 1, unitPrice: 125000, total: 125000 },
      { id: '2', description: 'Labor Costs', quantity: 1, unitPrice: 85000, total: 85000 },
      { id: '3', description: 'Equipment & Tools', quantity: 1, unitPrice: 15000, total: 15000 },
      { id: '4', description: 'Permits & Fees', quantity: 1, unitPrice: 5000, total: 5000 }
    ],
    subtotal: 230000,
    taxRate: 8.5,
    taxAmount: 19550,
    totalAmount: 249550,
    notes: '',
    terms: 'This quotation is valid for 30 days from the date issued. A 20% deposit is required to begin work.',
    clientSignature: '',
    signedDate: '',
    status: 'draft'
  });

  const [isAccepting, setIsAccepting] = useState(false);

  const handleInputChange = (field: keyof QuotationData, value: any) => {
    if (isReadOnly) return;
    setQuotationData(prev => ({ ...prev, [field]: value }));
  };

  const addLineItem = () => {
    if (isReadOnly) return;
    const newItem: LineItem = {
      id: Date.now().toString(),
      description: '',
      quantity: 1,
      unitPrice: 0,
      total: 0
    };
    setQuotationData(prev => ({
      ...prev,
      lineItems: [...prev.lineItems, newItem]
    }));
  };

  const updateLineItem = (id: string, field: keyof LineItem, value: any) => {
    if (isReadOnly) return;
    setQuotationData(prev => ({
      ...prev,
      lineItems: prev.lineItems.map(item => {
        if (item.id === id) {
          const updated = { ...item, [field]: value };
          if (field === 'quantity' || field === 'unitPrice') {
            updated.total = updated.quantity * updated.unitPrice;
          }
          return updated;
        }
        return item;
      })
    }));
    
    // Recalculate totals
    setTimeout(() => {
      setQuotationData(prev => {
        const subtotal = prev.lineItems.reduce((sum, item) => sum + item.total, 0);
        const taxAmount = subtotal * (prev.taxRate / 100);
        const totalAmount = subtotal + taxAmount;
        return { ...prev, subtotal, taxAmount, totalAmount };
      });
    }, 0);
  };

  const removeLineItem = (id: string) => {
    if (isReadOnly) return;
    setQuotationData(prev => ({
      ...prev,
      lineItems: prev.lineItems.filter(item => item.id !== id)
    }));
  };

  const handleAccept = async () => {
    setIsAccepting(true);
    // Simulate acceptance process
    setTimeout(() => {
      setQuotationData(prev => ({
        ...prev,
        clientSignature: prev.clientName,
        signedDate: new Date().toISOString().split('T')[0],
        status: 'accepted'
      }));
      setIsAccepting(false);
    }, 2000);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft': return 'bg-gray-100 text-gray-800';
      case 'sent': return 'bg-blue-100 text-blue-800';
      case 'accepted': return 'bg-green-100 text-green-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      case 'expired': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        {/* Header */}
        <div 
          className="rounded-lg p-6 mb-6 text-white"
          style={{ backgroundColor: customization.primaryColor }}
        >
          <div className="flex justify-between items-start">
            <div className="flex items-center space-x-4">
              {customization.logoUrl ? (
                <img 
                  src={customization.logoUrl} 
                  alt="Company Logo" 
                  className="w-16 h-16 bg-white rounded-lg p-2 object-contain"
                />
              ) : (
                <div 
                  className="w-16 h-16 bg-white rounded-lg flex items-center justify-center text-lg font-bold"
                  style={{ color: customization.primaryColor }}
                >
                  {(customization.companyName || "CO").substring(0, 2).toUpperCase()}
                </div>
              )}
              <div>
                <h1 className="text-2xl font-bold">{customization.companyName || "Your Company"}</h1>
                <p className="opacity-90">Project Quotation</p>
                <p className="text-sm opacity-75">Template: {template.name}</p>
              </div>
            </div>
            <div className="text-right text-sm">
              <p>{customization.companyPhone || "(*************"}</p>
              <p>{customization.companyEmail || "<EMAIL>"}</p>
              {customization.companyWebsite && <p>{customization.companyWebsite}</p>}
            </div>
          </div>
        </div>

        {/* Status Badge */}
        <div className="mb-6">
          <Badge className={`${getStatusColor(quotationData.status)} text-sm px-3 py-1`}>
            {quotationData.status === 'draft' && <Clock className="w-4 h-4 mr-1" />}
            {quotationData.status === 'sent' && <FileText className="w-4 h-4 mr-1" />}
            {quotationData.status === 'accepted' && <CheckCircle className="w-4 h-4 mr-1" />}
            {quotationData.status.charAt(0).toUpperCase() + quotationData.status.slice(1)}
          </Badge>
        </div>

        {/* Client Information */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <User className="w-5 h-5" />
              <span>Client Information</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">Client Name *</label>
              <Input
                value={quotationData.clientName}
                onChange={(e) => handleInputChange('clientName', e.target.value)}
                placeholder="Enter client name"
                disabled={isReadOnly}
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Email *</label>
              <Input
                type="email"
                value={quotationData.clientEmail}
                onChange={(e) => handleInputChange('clientEmail', e.target.value)}
                placeholder="<EMAIL>"
                disabled={isReadOnly}
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Phone</label>
              <Input
                value={quotationData.clientPhone}
                onChange={(e) => handleInputChange('clientPhone', e.target.value)}
                placeholder="(*************"
                disabled={isReadOnly}
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Address</label>
              <Input
                value={quotationData.clientAddress}
                onChange={(e) => handleInputChange('clientAddress', e.target.value)}
                placeholder="Client address"
                disabled={isReadOnly}
              />
            </div>
          </CardContent>
        </Card>

        {/* Project Details */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Building2 className="w-5 h-5" />
              <span>Project Details</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-1">Project Title *</label>
              <Input
                value={quotationData.projectTitle}
                onChange={(e) => handleInputChange('projectTitle', e.target.value)}
                placeholder="Enter project title"
                disabled={isReadOnly}
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Project Description *</label>
              <Textarea
                value={quotationData.projectDescription}
                onChange={(e) => handleInputChange('projectDescription', e.target.value)}
                placeholder="Detailed description of the project scope..."
                rows={4}
                disabled={isReadOnly}
              />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-1">Project Address</label>
                <Input
                  value={quotationData.projectAddress}
                  onChange={(e) => handleInputChange('projectAddress', e.target.value)}
                  placeholder="Project location"
                  disabled={isReadOnly}
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Valid Until</label>
                <Input
                  type="date"
                  value={quotationData.validUntil}
                  onChange={(e) => handleInputChange('validUntil', e.target.value)}
                  disabled={isReadOnly}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Line Items */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <DollarSign className="w-5 h-5" />
                <span>Cost Breakdown</span>
              </div>
              {!isReadOnly && (
                <Button variant="outline" size="sm" onClick={addLineItem}>
                  <Plus className="w-4 h-4 mr-1" />
                  Add Item
                </Button>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {quotationData.lineItems.map((item, index) => (
                <div key={item.id} className="grid grid-cols-12 gap-2 items-center">
                  <div className="col-span-5">
                    <Input
                      value={item.description}
                      onChange={(e) => updateLineItem(item.id, 'description', e.target.value)}
                      placeholder="Item description"
                      disabled={isReadOnly}
                    />
                  </div>
                  <div className="col-span-2">
                    <Input
                      type="number"
                      value={item.quantity}
                      onChange={(e) => updateLineItem(item.id, 'quantity', parseFloat(e.target.value) || 0)}
                      placeholder="Qty"
                      disabled={isReadOnly}
                    />
                  </div>
                  <div className="col-span-2">
                    <Input
                      type="number"
                      value={item.unitPrice}
                      onChange={(e) => updateLineItem(item.id, 'unitPrice', parseFloat(e.target.value) || 0)}
                      placeholder="Unit Price"
                      disabled={isReadOnly}
                    />
                  </div>
                  <div className="col-span-2">
                    <Input
                      value={`$${item.total.toLocaleString()}`}
                      disabled
                      className="bg-gray-50"
                    />
                  </div>
                  <div className="col-span-1">
                    {!isReadOnly && quotationData.lineItems.length > 1 && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeLineItem(item.id)}
                        className="text-red-500 hover:text-red-700"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    )}
                  </div>
                </div>
              ))}
              
              {/* Totals */}
              <div className="border-t pt-4 space-y-2">
                <div className="flex justify-between">
                  <span>Subtotal:</span>
                  <span className="font-medium">${quotationData.subtotal.toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span>Tax ({quotationData.taxRate}%):</span>
                  <span className="font-medium">${quotationData.taxAmount.toLocaleString()}</span>
                </div>
                <div className="flex justify-between text-lg font-bold border-t pt-2">
                  <span>Total:</span>
                  <span>${quotationData.totalAmount.toLocaleString()}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Terms and Notes */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Terms & Notes</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-1">Terms & Conditions</label>
              <Textarea
                value={quotationData.terms}
                onChange={(e) => handleInputChange('terms', e.target.value)}
                rows={3}
                disabled={isReadOnly}
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Additional Notes</label>
              <Textarea
                value={quotationData.notes}
                onChange={(e) => handleInputChange('notes', e.target.value)}
                placeholder="Any additional notes or specifications..."
                rows={3}
                disabled={isReadOnly}
              />
            </div>
          </CardContent>
        </Card>

        {/* Accept Quotation */}
        {!isReadOnly && quotationData.status !== 'accepted' && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Signature className="w-5 h-5" />
                <span>Accept Quotation</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <p className="text-gray-600 mb-4">
                  By clicking "Accept Quotation", you agree to proceed with this project at the quoted price.
                </p>
                <Button 
                  onClick={handleAccept}
                  disabled={isAccepting || !quotationData.clientName || !quotationData.clientEmail}
                  className="px-8 py-3"
                  style={{ backgroundColor: customization.primaryColor }}
                >
                  {isAccepting ? (
                    <>
                      <Clock className="w-4 h-4 mr-2 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    <>
                      <CheckCircle className="w-4 h-4 mr-2" />
                      Accept Quotation
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Accepted Quotation Display */}
        {quotationData.status === 'accepted' && (
          <Card className="mb-6 border-green-200 bg-green-50">
            <CardContent className="pt-6">
              <div className="text-center">
                <CheckCircle className="w-12 h-12 text-green-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-green-800 mb-2">Quotation Accepted!</h3>
                <p className="text-green-700 mb-4">
                  This quotation was accepted by {quotationData.clientSignature} on {quotationData.signedDate}
                </p>
                <div className="bg-white rounded-lg p-4 border border-green-200">
                  <p className="text-sm text-gray-600">Digital Acceptance:</p>
                  <p className="font-semibold text-lg">{quotationData.clientSignature}</p>
                  <p className="text-sm text-gray-500">Accepted on: {quotationData.signedDate}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default QuotationForm;
