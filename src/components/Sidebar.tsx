import { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import {
  Settings,
  Users,
  Calendar,
  FileText,
  Search,
  File,
  Clock,
  CheckSquare,
  User,
  LogOut,
  ChevronLeft,
  ChevronRight,
  Home,
  Menu,
  X,
  FolderOpen,
  Share2,
  Calculator,
  Building2,
  Grid3X3
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { cn } from '@/lib/utils';

const Sidebar = () => {
  const location = useLocation();
  const { user, logout } = useAuth();
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isMobileOpen, setIsMobileOpen] = useState(false);

  const isActive = (path: string) => location.pathname === path;

  const getUserInitials = () => {
    if (!user || !user.user_metadata) return "U";
    const firstName = user.user_metadata.first_name || "";
    const lastName = user.user_metadata.last_name || "";
    if (!firstName && !lastName) return "U";
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  const handleLogout = () => {
    logout();
    setIsMobileOpen(false);
  };

  const navigationItems = [
    {
      path: "/dashboard",
      label: "Dashboard",
      icon: Home,
      description: "Project overview"
    },
    {
      path: "/projects",
      label: "Projects",
      icon: FolderOpen,
      description: "All projects"
    },
    {
      path: "/tasks",
      label: "Tasks",
      icon: CheckSquare,
      description: "Task management"
    },
    {
      path: "/contracts",
      label: "Contracts",
      icon: File,
      description: "Document management"
    },
    {
      path: "/client-portal",
      label: "Client Portal",
      icon: Share2,
      description: "Client access"
    },
    {
      path: "/settings",
      label: "Settings",
      icon: Settings,
      description: "Account settings"
    },
  ];

  const moduleItems = [
    {
      path: "/module-selection",
      label: "All Modules",
      icon: Grid3X3,
      description: "Switch modules"
    },
    {
      path: "/quotation-dashboard",
      label: "Quotation Builder",
      icon: Calculator,
      description: "Create quotations"
    },
    {
      path: "/contract-dashboard",
      label: "Contract Builder",
      icon: FileText,
      description: "Manage contracts"
    },
  ];

  const SidebarContent = () => (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="p-4 border-b">
        <div className="flex items-center justify-between">
          <Link to="/dashboard" className="flex items-center space-x-2">
            <div className="h-8 w-8 construction-gradient rounded-lg flex items-center justify-center">
              <Settings className="h-5 w-5 text-white" />
            </div>
            {!isCollapsed && (
              <span className="text-lg font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
                ConstructionSync
              </span>
            )}
          </Link>
          
          {/* Desktop collapse button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="hidden lg:flex h-8 w-8 p-0"
          >
            {isCollapsed ? (
              <ChevronRight className="h-4 w-4" />
            ) : (
              <ChevronLeft className="h-4 w-4" />
            )}
          </Button>
          
          {/* Mobile close button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsMobileOpen(false)}
            className="lg:hidden h-8 w-8 p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4 space-y-2">
        {navigationItems.map((item) => (
          <Link
            key={item.path}
            to={item.path}
            onClick={() => setIsMobileOpen(false)}
            className={cn(
              "flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors group",
              isActive(item.path)
                ? "bg-primary text-primary-foreground"
                : "hover:bg-muted text-muted-foreground hover:text-foreground"
            )}
          >
            <item.icon className="h-5 w-5 flex-shrink-0" />
            {!isCollapsed && (
              <div className="flex-1 min-w-0">
                <div className="font-medium">{item.label}</div>
                <div className="text-xs opacity-70 truncate">{item.description}</div>
              </div>
            )}
          </Link>
        ))}

      </nav>

      {/* Module Navigation */}
      <div className="px-4 py-2 border-t">
        {!isCollapsed && (
          <div className="text-xs font-semibold text-muted-foreground uppercase tracking-wider mb-2">
            Other Modules
          </div>
        )}
        <div className="space-y-1">
          {moduleItems.map((item) => (
            <Link
              key={item.path}
              to={item.path}
              onClick={() => setIsMobileOpen(false)}
              className={cn(
                "flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors group text-sm",
                isActive(item.path)
                  ? "bg-primary text-primary-foreground"
                  : "hover:bg-muted text-muted-foreground hover:text-foreground"
              )}
            >
              <item.icon className="h-4 w-4 flex-shrink-0" />
              {!isCollapsed && (
                <div className="flex-1 min-w-0">
                  <div className="font-medium">{item.label}</div>
                  <div className="text-xs opacity-70 truncate">{item.description}</div>
                </div>
              )}
            </Link>
          ))}
        </div>
      </div>

      {/* User Profile */}
      <div className="p-4 border-t">
        {user && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className={cn(
                  "w-full justify-start p-2 h-auto",
                  isCollapsed ? "px-2" : "px-3"
                )}
              >
                <div className="flex items-center space-x-3 min-w-0 flex-1">
                  <Avatar className="h-8 w-8 flex-shrink-0">
                    <AvatarFallback className="bg-primary text-primary-foreground text-sm">
                      {getUserInitials()}
                    </AvatarFallback>
                  </Avatar>
                  {!isCollapsed && (
                    <div className="flex-1 min-w-0 text-left">
                      <div className="font-medium truncate">
                        {user.firstName} {user.lastName}
                      </div>
                      <div className="text-xs text-muted-foreground truncate">
                        {user.email}
                      </div>
                      <Badge variant="outline" className="text-xs mt-1">
                        {user.role}
                      </Badge>
                    </div>
                  )}
                </div>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56" align="end" forceMount>
              <div className="flex items-center justify-start gap-2 p-2">
                <div className="flex flex-col space-y-1 leading-none">
                  <p className="font-medium">{user.firstName} {user.lastName}</p>
                  <p className="w-[200px] truncate text-sm text-muted-foreground">
                    {user.email}
                  </p>
                </div>
              </div>
              <DropdownMenuSeparator />
              <DropdownMenuItem asChild>
                <Link to="/profile">
                  <User className="mr-2 h-4 w-4" />
                  Profile Settings
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link to="/dashboard">
                  <Settings className="mr-2 h-4 w-4" />
                  Dashboard
                </Link>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleLogout}>
                <LogOut className="mr-2 h-4 w-4" />
                Sign out
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>
    </div>
  );

  return (
    <>
      {/* Desktop Sidebar */}
      <aside
        className={cn(
          "hidden lg:flex flex-col bg-background border-r transition-all duration-300",
          isCollapsed ? "w-16" : "w-64"
        )}
      >
        <SidebarContent />
      </aside>

      {/* Mobile Sidebar */}
      {isMobileOpen && (
        <div className="lg:hidden fixed inset-0 z-50 flex">
          <div className="fixed inset-0 bg-black/50" onClick={() => setIsMobileOpen(false)} />
          <aside className="relative flex flex-col w-64 bg-background border-r">
            <SidebarContent />
          </aside>
        </div>
      )}

      {/* Mobile Menu Button */}
      <Button
        variant="outline"
        size="sm"
        onClick={() => setIsMobileOpen(true)}
        className="lg:hidden fixed top-4 left-4 z-40 h-10 w-10 p-0"
      >
        <Menu className="h-4 w-4" />
      </Button>
    </>
  );
};

export default Sidebar;
