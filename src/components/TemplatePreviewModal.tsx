import { useState } from "react";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Download,
  Star,
  Eye,
  FileText,
  Palette,
  Settings,
  CheckCircle,
  DollarSign,
  Calendar,
  User,
  Paintbrush
} from "lucide-react";

interface TemplatePreviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  template: {
    id: string;
    name: string;
    description: string;
    category: string;
    type: string;
    rating: number;
    downloads: number;
    features: string[];
    preview: string;
  } | null;
  onUseTemplate: (templateId: string) => void;
  onCustomizeTemplate: (template: any) => void;
}

const TemplatePreviewModal = ({ isOpen, onClose, template, onUseTemplate, onCustomizeTemplate }: TemplatePreviewModalProps) => {
  const [activeTab, setActiveTab] = useState("preview");

  if (!template) return null;

  const getPreviewContent = () => {
    if (template.category === "classic") {
      return (
        <div className="bg-white p-8 border rounded-lg font-serif">
          <div className="text-center mb-8">
            <h1 className="text-2xl font-bold text-gray-900 mb-2">CONSTRUCTION CONTRACT</h1>
            <p className="text-gray-600">Professional Agreement</p>
          </div>
          
          <div className="space-y-6">
            <div className="grid grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Contractor Information</h3>
                <div className="space-y-1 text-sm text-gray-600">
                  <p>ABC Construction Company</p>
                  <p>123 Builder Street</p>
                  <p>City, State 12345</p>
                  <p>Phone: (*************</p>
                </div>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Client Information</h3>
                <div className="space-y-1 text-sm text-gray-600">
                  <p>John & Jane Smith</p>
                  <p>456 Homeowner Ave</p>
                  <p>City, State 12345</p>
                  <p>Phone: (*************</p>
                </div>
              </div>
            </div>
            
            <div className="border-t pt-4">
              <h3 className="font-semibold text-gray-900 mb-2">Project Details</h3>
              <p className="text-sm text-gray-600">
                Construction of a two-story residential home including foundation, framing, 
                electrical, plumbing, and finishing work as specified in the attached plans.
              </p>
            </div>
            
            <div className="border-t pt-4">
              <h3 className="font-semibold text-gray-900 mb-2">Cost Breakdown</h3>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="space-y-1">
                  <div className="flex justify-between">
                    <span>Materials:</span>
                    <span>$125,000</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Labor:</span>
                    <span>$85,000</span>
                  </div>
                </div>
                <div className="space-y-1">
                  <div className="flex justify-between">
                    <span>Equipment:</span>
                    <span>$15,000</span>
                  </div>
                  <div className="flex justify-between font-semibold border-t pt-1">
                    <span>Total:</span>
                    <span>$225,000</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    } else if (template.category === "modern") {
      return (
        <div className="bg-gradient-to-br from-blue-50 to-white p-8 border rounded-lg">
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Construction Agreement</h1>
              <p className="text-blue-600 font-medium">Modern Professional Contract</p>
            </div>
            <div className="text-right">
              <Badge className="bg-blue-100 text-blue-800">Contract #2024-001</Badge>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2 mb-2">
                  <User className="h-4 w-4 text-blue-600" />
                  <span className="font-medium">Contractor</span>
                </div>
                <p className="text-sm text-gray-600">Modern Builders Inc.</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2 mb-2">
                  <DollarSign className="h-4 w-4 text-green-600" />
                  <span className="font-medium">Total Value</span>
                </div>
                <p className="text-lg font-bold text-green-600">$285,000</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2 mb-2">
                  <Calendar className="h-4 w-4 text-orange-600" />
                  <span className="font-medium">Timeline</span>
                </div>
                <p className="text-sm text-gray-600">6 months</p>
              </CardContent>
            </Card>
          </div>
          
          <div className="space-y-4">
            <div className="bg-white p-4 rounded-lg border">
              <h3 className="font-semibold text-gray-900 mb-2">Project Scope</h3>
              <p className="text-sm text-gray-600">
                Complete residential construction including modern design elements, 
                smart home integration, and sustainable building practices.
              </p>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="bg-white p-4 rounded-lg border">
                <h4 className="font-medium text-gray-900 mb-2">Key Features</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li className="flex items-center space-x-2">
                    <CheckCircle className="h-3 w-3 text-green-500" />
                    <span>Digital progress tracking</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <CheckCircle className="h-3 w-3 text-green-500" />
                    <span>Real-time updates</span>
                  </li>
                </ul>
              </div>
              <div className="bg-white p-4 rounded-lg border">
                <h4 className="font-medium text-gray-900 mb-2">Payment Schedule</h4>
                <div className="text-sm text-gray-600 space-y-1">
                  <div className="flex justify-between">
                    <span>Down Payment:</span>
                    <span>20%</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Progress Payments:</span>
                    <span>60%</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Final Payment:</span>
                    <span>20%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    } else if (template.category === "creative") {
      return (
        <div className="bg-gradient-to-br from-purple-50 via-pink-50 to-white p-8 border rounded-lg">
          <div className="text-center mb-8">
            <div className="inline-flex items-center space-x-2 mb-4">
              <Palette className="h-6 w-6 text-purple-600" />
              <h1 className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                Creative Construction Contract
              </h1>
            </div>
            <p className="text-gray-600">Innovative Design & Build Agreement</p>
          </div>
          
          <div className="space-y-6">
            <div className="bg-white/80 backdrop-blur-sm p-6 rounded-xl border border-purple-200">
              <h3 className="font-semibold text-gray-900 mb-4 flex items-center space-x-2">
                <span className="w-2 h-2 bg-purple-500 rounded-full"></span>
                <span>Project Vision</span>
              </h3>
              <p className="text-gray-600 italic">
                "Creating a unique living space that reflects the client's artistic vision 
                while maintaining structural integrity and modern functionality."
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-white/80 backdrop-blur-sm p-6 rounded-xl border border-pink-200">
                <h4 className="font-medium text-gray-900 mb-3">Creative Elements</h4>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li>• Custom artistic installations</li>
                  <li>• Unique material combinations</li>
                  <li>• Innovative lighting design</li>
                  <li>• Personalized color schemes</li>
                </ul>
              </div>
              <div className="bg-white/80 backdrop-blur-sm p-6 rounded-xl border border-purple-200">
                <h4 className="font-medium text-gray-900 mb-3">Investment Breakdown</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Design & Planning:</span>
                    <span className="font-medium">$25,000</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Materials & Labor:</span>
                    <span className="font-medium">$185,000</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Creative Features:</span>
                    <span className="font-medium">$35,000</span>
                  </div>
                  <div className="flex justify-between border-t pt-2 font-semibold">
                    <span>Total Investment:</span>
                    <span className="text-purple-600">$245,000</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    } else {
      return (
        <div className="bg-white p-8 border rounded-lg">
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">LEGAL CONSTRUCTION CONTRACT</h1>
              <p className="text-gray-600">Comprehensive Legal Protection</p>
            </div>
            <Badge className="bg-green-100 text-green-800">Legally Verified</Badge>
          </div>
          
          <div className="space-y-6">
            <div className="bg-green-50 p-4 rounded-lg border border-green-200">
              <h3 className="font-semibold text-green-900 mb-2">Legal Compliance Features</h3>
              <ul className="text-sm text-green-800 space-y-1">
                <li>✓ Industry-standard legal clauses</li>
                <li>✓ Comprehensive risk mitigation</li>
                <li>✓ Dispute resolution procedures</li>
                <li>✓ Insurance and bonding requirements</li>
              </ul>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold text-gray-900 mb-3">Contract Terms</h4>
                <div className="space-y-2 text-sm text-gray-600">
                  <p><strong>Governing Law:</strong> State Construction Code</p>
                  <p><strong>Warranty Period:</strong> 2 Years</p>
                  <p><strong>Liability Coverage:</strong> $2,000,000</p>
                  <p><strong>Performance Bond:</strong> Required</p>
                </div>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900 mb-3">Payment Protection</h4>
                <div className="space-y-2 text-sm text-gray-600">
                  <p><strong>Lien Waiver:</strong> Progressive</p>
                  <p><strong>Retainage:</strong> 10% until completion</p>
                  <p><strong>Change Orders:</strong> Written approval required</p>
                  <p><strong>Final Payment:</strong> 30 days after completion</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Eye className="h-5 w-5" />
            <span>{template.name}</span>
          </DialogTitle>
          <DialogDescription>
            {template.description}
          </DialogDescription>
        </DialogHeader>
        
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="preview">Preview</TabsTrigger>
            <TabsTrigger value="features">Features</TabsTrigger>
            <TabsTrigger value="details">Details</TabsTrigger>
          </TabsList>
          
          <TabsContent value="preview" className="mt-6">
            <div className="border rounded-lg p-4 bg-gray-50 max-h-96 overflow-y-auto">
              {getPreviewContent()}
            </div>
          </TabsContent>
          
          <TabsContent value="features" className="mt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {template.features.map((feature, index) => (
                <div key={index} className="flex items-center space-x-2 p-3 bg-gray-50 rounded-lg">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-sm">{feature}</span>
                </div>
              ))}
            </div>
          </TabsContent>
          
          <TabsContent value="details" className="mt-6">
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Template Information</h4>
                  <div className="space-y-2 text-sm text-gray-600">
                    <p><strong>Category:</strong> {template.category}</p>
                    <p><strong>Type:</strong> {template.type}</p>
                    <p><strong>Rating:</strong> {template.rating}/5.0</p>
                    <p><strong>Downloads:</strong> {template.downloads}</p>
                  </div>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Usage Guidelines</h4>
                  <div className="space-y-2 text-sm text-gray-600">
                    <p>• Customize all placeholder text</p>
                    <p>• Review legal clauses with attorney</p>
                    <p>• Adjust pricing and terms as needed</p>
                    <p>• Ensure compliance with local laws</p>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
        
        <div className="flex justify-between items-center pt-6 border-t">
          <div className="flex items-center space-x-2">
            <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
            <span className="text-sm text-gray-600">{template.rating} ({template.downloads} downloads)</span>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
            <Button variant="outline" onClick={() => onCustomizeTemplate(template)}>
              <Paintbrush className="h-4 w-4 mr-2" />
              Customize
            </Button>
            <Button onClick={() => onUseTemplate(template.id)}>
              <Download className="h-4 w-4 mr-2" />
              Use This Template
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default TemplatePreviewModal;
