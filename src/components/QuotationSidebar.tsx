import { useState } from "react";
import { Link, useLocation } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Calculator,
  LayoutDashboard,
  Edit,
  CheckCircle,
  Clock,
  XCircle,
  AlertCircle,
  FileImage,
  LogOut,
  Menu,
  X,
  User,
  FileText,
  Building2,
  Grid3X3
} from "lucide-react";
import { useQuotationAuth } from "@/contexts/QuotationAuthContext";
import { useAuth } from "@/contexts/AuthContext";
import { cn } from "@/lib/utils";

interface QuotationSidebarProps {
  className?: string;
  onMobileMenuToggle?: () => void;
  mobileMenuOpen?: boolean;
}

const QuotationSidebar = ({ className, onMobileMenuToggle }: QuotationSidebarProps) => {
  const { quotations } = useQuotationAuth();
  const { user, logout } = useAuth();
  const location = useLocation();
  const [isCollapsed, setIsCollapsed] = useState(false);

  const quotationStats = {
    active: quotations.filter(q => q.status === 'sent').length,
    draft: quotations.filter(q => q.status === 'draft').length,
    approved: quotations.filter(q => q.status === 'approved').length,
    rejected: quotations.filter(q => q.status === 'rejected' || q.status === 'expired').length,
  };

  const navigationItems = [
    {
      title: "Dashboard",
      href: "/quotation-dashboard",
      icon: LayoutDashboard,
      count: null
    },
    {
      title: "Active Quotations",
      href: "/quotation-dashboard?filter=sent",
      icon: Clock,
      count: quotationStats.active,
      color: "text-blue-600"
    },
    {
      title: "Draft Quotations",
      href: "/quotation-dashboard?filter=draft",
      icon: Edit,
      count: quotationStats.draft,
      color: "text-gray-600"
    },
    {
      title: "Approved Quotes",
      href: "/quotation-dashboard?filter=approved",
      icon: CheckCircle,
      count: quotationStats.approved,
      color: "text-green-600"
    },
    {
      title: "Rejected/Expired",
      href: "/quotation-dashboard?filter=rejected",
      icon: XCircle,
      count: quotationStats.rejected,
      color: "text-red-600"
    },
    {
      title: "Templates",
      href: "/quotation-templates",
      icon: FileImage,
      count: null
    }
  ];

  const moduleItems = [
    {
      title: "All Modules",
      href: "/module-selection",
      icon: Grid3X3,
      count: null
    },
    {
      title: "ConstructionSync Platform",
      href: "/dashboard",
      icon: Building2,
      count: null
    },
    {
      title: "Contract Builder",
      href: "/contract-dashboard",
      icon: FileText,
      count: null
    }
  ];

  const isActive = (href: string) => {
    if (href === "/quotation-dashboard") {
      return location.pathname === "/quotation-dashboard" && !location.search;
    }
    return location.pathname + location.search === href;
  };

  return (
    <div className={cn(
      "flex flex-col h-full bg-white border-r border-gray-200 transition-all duration-300",
      isCollapsed ? "w-16" : "w-64",
      className
    )}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b">
        {!isCollapsed && (
          <div className="flex items-center space-x-2">
            <div className="h-8 w-8 bg-accent rounded-lg flex items-center justify-center">
              <Calculator className="h-5 w-5 text-white" />
            </div>
            <div>
              <h2 className="text-lg font-bold text-accent">Quotation Builder</h2>
              <p className="text-xs text-muted-foreground">Professional Edition</p>
            </div>
          </div>
        )}
        <div className="flex items-center space-x-2">
          {/* Mobile Close Button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={onMobileMenuToggle}
            className="h-8 w-8 p-0 lg:hidden"
          >
            <X className="h-4 w-4" />
          </Button>
          {/* Desktop Collapse Button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="h-8 w-8 p-0 hidden lg:flex"
          >
            {isCollapsed ? <Menu className="h-4 w-4" /> : <X className="h-4 w-4" />}
          </Button>
        </div>
      </div>

      {/* User Profile */}
      <div className="p-4 border-b">
        <div className="flex items-center space-x-3">
          <div className="h-10 w-10 bg-accent/10 rounded-full flex items-center justify-center">
            <User className="h-5 w-5 text-accent" />
          </div>
          {!isCollapsed && (
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900 truncate">{user?.user_metadata?.full_name || user?.email}</p>
              <p className="text-xs text-muted-foreground truncate">{user?.email}</p>
            </div>
          )}
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4 space-y-2">
        {navigationItems.map((item) => {
          const Icon = item.icon;
          const active = isActive(item.href);
          
          return (
            <Link
              key={item.href}
              to={item.href}
              className={cn(
                "flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors",
                active 
                  ? "bg-accent text-white" 
                  : "text-gray-700 hover:bg-gray-100",
                isCollapsed && "justify-center"
              )}
            >
              <Icon className={cn("h-5 w-5", item.color && !active ? item.color : "")} />
              {!isCollapsed && (
                <>
                  <span className="flex-1">{item.title}</span>
                  {item.count !== null && item.count > 0 && (
                    <Badge 
                      variant={active ? "secondary" : "outline"} 
                      className="text-xs"
                    >
                      {item.count}
                    </Badge>
                  )}
                </>
              )}
            </Link>
          );
        })}
      </nav>

      {/* Module Navigation */}
      <div className="p-4 border-t">
        {!isCollapsed && (
          <div className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">
            Other Modules
          </div>
        )}
        <div className="space-y-1">
          {moduleItems.map((item) => {
            const Icon = item.icon;
            const active = isActive(item.href);

            return (
              <Link
                key={item.href}
                to={item.href}
                className={cn(
                  "flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors",
                  active
                    ? "bg-accent text-white"
                    : "text-gray-700 hover:bg-gray-100",
                  isCollapsed && "justify-center"
                )}
              >
                <Icon className="h-4 w-4" />
                {!isCollapsed && (
                  <span className="flex-1">{item.title}</span>
                )}
              </Link>
            );
          })}
        </div>

        {/* Quick Action */}
        {!isCollapsed && (
          <div className="mt-3">
            <Button asChild className="w-full bg-accent hover:bg-accent/90" size="sm">
              <Link to="/quotations" className="flex items-center space-x-2">
                <Calculator className="h-4 w-4" />
                <span>New Quotation</span>
              </Link>
            </Button>
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="p-4 border-t">
        <Button
          variant="ghost"
          onClick={logout}
          className={cn(
            "w-full flex items-center space-x-2 text-red-600 hover:text-red-700 hover:bg-red-50",
            isCollapsed && "justify-center"
          )}
          size="sm"
        >
          <LogOut className="h-4 w-4" />
          {!isCollapsed && <span>Sign Out</span>}
        </Button>
      </div>
    </div>
  );
};

export default QuotationSidebar;
