import { ReactNode } from "react";
import { Navigate } from "react-router-dom";
import { useContractAuth } from "@/contexts/ContractAuthContext";

interface ContractProtectedRouteProps {
  children: ReactNode;
}

const ContractProtectedRoute = ({ children }: ContractProtectedRouteProps) => {
  const { isAuthenticated, isLoading } = useContractAuth();

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return <Navigate to="/contract-login" replace />;
  }

  return <>{children}</>;
};

export default ContractProtectedRoute;
