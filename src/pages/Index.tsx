
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Users, Calendar, Settings, FileText, Plus, Search } from "lucide-react";
import { Link } from "react-router-dom";

const Index = () => {
  const features = [
    {
      icon: <Settings className="h-8 w-8" />,
      title: "Project Management",
      description: "Centralized dashboard for complete project oversight with real-time updates",
      badge: "Core Feature"
    },
    {
      icon: <Calendar className="h-8 w-8" />,
      title: "Cost & Budget Tracking",
      description: "Advanced expense monitoring with automated alerts and forecasting",
      badge: "Financial"
    },
    {
      icon: <Users className="h-8 w-8" />,
      title: "Labor Management",
      description: "Workforce tracking, time entries, and payroll management with skill-based assignments",
      badge: "Workforce"
    },
    {
      icon: <FileText className="h-8 w-8" />,
      title: "Material Management",
      description: "Complete inventory tracking with supplier integration and automated reordering",
      badge: "Inventory"
    },
    {
      icon: <Plus className="h-8 w-8" />,
      title: "Task Management",
      description: "Organize, assign, and track tasks with Kanban boards and progress monitoring",
      badge: "Productivity"
    },
    {
      icon: <Search className="h-8 w-8" />,
      title: "Client Portal",
      description: "Secure client access with unique links and real-time project visibility",
      badge: "Client Access"
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b bg-white/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="h-8 w-8 construction-gradient rounded-lg flex items-center justify-center">
                <Settings className="h-5 w-5 text-white" />
              </div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
                ConstructionSync
              </h1>
            </div>
            <nav className="hidden md:flex items-center space-x-6">
              <Link to="/dashboard" className="text-muted-foreground hover:text-primary transition-colors">
                Dashboard
              </Link>
              <Link to="/costs" className="text-muted-foreground hover:text-primary transition-colors">
                Cost Tracking
              </Link>
              <Link to="/labor" className="text-muted-foreground hover:text-primary transition-colors">
                Labor
              </Link>
              <Link to="/tasks" className="text-muted-foreground hover:text-primary transition-colors">
                Tasks
              </Link>
              <Link to="/materials" className="text-muted-foreground hover:text-primary transition-colors">
                Materials
              </Link>
              <Button asChild>
                <Link to="/dashboard">Get Started</Link>
              </Button>
            </nav>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="hero-pattern py-20 md:py-32">
        <div className="container mx-auto px-4 text-center">
          <div className="max-w-4xl mx-auto">
            <Badge variant="secondary" className="mb-4">
              Real-Time Construction Management Platform
            </Badge>
            <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
              Sync Your Construction Projects in 
              <span className="bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent"> Real-Time</span>
            </h1>
            <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
              Complete project visibility for contractors and clients. Track progress, manage costs, 
              and collaborate seamlessly with our modern construction management platform.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" asChild className="text-lg">
                <Link to="/dashboard">
                  <Plus className="mr-2 h-5 w-5" />
                  Start Managing Projects
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild className="text-lg">
                <Link to="/client/demo-token">
                  <Search className="mr-2 h-5 w-5" />
                  View Client Portal Demo
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Features Grid */}
      <section className="py-20 bg-muted/30">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Everything You Need to Manage Construction Projects
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              From initial planning to project completion, our platform provides all the tools 
              contractors and clients need for successful project delivery.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {features.map((feature, index) => (
              <Card key={index} className="group hover:shadow-lg transition-all duration-300 border-2 hover:border-primary/20">
                <CardHeader className="text-center">
                  <div className="mx-auto mb-4 p-3 bg-primary/10 rounded-lg text-primary group-hover:bg-primary group-hover:text-white transition-colors">
                    {feature.icon}
                  </div>
                  <Badge variant="outline" className="mb-2 w-fit mx-auto">
                    {feature.badge}
                  </Badge>
                  <CardTitle className="text-xl">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-center">
                    {feature.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Key Benefits */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <Badge variant="secondary" className="mb-4">
                Real-Time Synchronization
              </Badge>
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Instant Updates, Perfect Transparency
              </h2>
              <div className="space-y-6">
                <div className="flex items-start space-x-4">
                  <div className="p-2 bg-primary/10 rounded-lg">
                    <Users className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-semibold mb-2">Unique Client Access Links</h3>
                    <p className="text-muted-foreground">
                      Generate secure, unique URLs for each client to access real-time project updates 
                      without requiring full platform accounts.
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-4">
                  <div className="p-2 bg-accent/10 rounded-lg">
                    <Calendar className="h-5 w-5 text-accent" />
                  </div>
                  <div>
                    <h3 className="font-semibold mb-2">Live Progress Updates</h3>
                    <p className="text-muted-foreground">
                      All project changes sync instantly across contractor dashboards and client portals 
                      for complete transparency.
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-4">
                  <div className="p-2 bg-primary/10 rounded-lg">
                    <FileText className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-semibold mb-2">Comprehensive Cost Tracking</h3>
                    <p className="text-muted-foreground">
                      Monitor materials, labor, and expenses with automatic budget alerts and 
                      detailed reporting capabilities.
                    </p>
                  </div>
                </div>
              </div>
              <Button size="lg" asChild className="mt-8">
                <Link to="/dashboard">
                  Explore Platform Features
                </Link>
              </Button>
            </div>
            <div className="bg-gradient-to-br from-primary/5 to-accent/5 p-8 rounded-2xl">
              <div className="bg-white rounded-xl p-6 shadow-lg">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="font-semibold">Office Complex Project</h3>
                  <Badge variant="default">75% Complete</Badge>
                </div>
                <div className="space-y-3">
                  <div className="flex justify-between text-sm">
                    <span>Progress</span>
                    <span className="font-medium">75%</span>
                  </div>
                  <div className="w-full bg-muted rounded-full h-2">
                    <div className="bg-primary h-2 rounded-full w-3/4"></div>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Budget Used</span>
                    <span className="text-green-600 font-medium">$185K / $250K</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Completion Date</span>
                    <span className="font-medium">Dec 15, 2025</span>
                  </div>
                </div>
                <div className="mt-4 pt-4 border-t">
                  <p className="text-xs text-muted-foreground">
                    ✓ Last updated 2 minutes ago
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="border-t py-12 bg-muted/30">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center space-x-2 mb-4 md:mb-0">
              <div className="h-6 w-6 construction-gradient rounded"></div>
              <span className="font-semibold">ConstructionSync Platform</span>
            </div>
            <div className="flex space-x-6">
              <Link to="/dashboard" className="text-muted-foreground hover:text-primary transition-colors">
                Dashboard
              </Link>
              <Link to="/costs" className="text-muted-foreground hover:text-primary transition-colors">
                Cost Tracking
              </Link>
              <Link to="/materials" className="text-muted-foreground hover:text-primary transition-colors">
                Materials
              </Link>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Index;
