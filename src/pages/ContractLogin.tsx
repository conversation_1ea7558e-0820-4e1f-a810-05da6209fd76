import { useState } from "react";
import { <PERSON>, useNavigate } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { FileText, Settings, Loader2, ArrowLeft } from "lucide-react";
import { useContractAuth } from "@/contexts/ContractAuthContext";

const ContractLogin = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const { login } = useContractAuth();
  const navigate = useNavigate();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    setIsLoading(true);

    try {
      const success = await login(email, password);
      if (success) {
        navigate("/contract-dashboard");
      } else {
        setError("Invalid email or password. Please try again.");
      }
    } catch (err) {
      setError("An error occurred during login. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50">
      {/* Header */}
      <header className="border-b bg-white/80 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <Link to="/professional-selection" className="flex items-center space-x-2">
              <div className="h-8 w-8 construction-gradient rounded-lg flex items-center justify-center">
                <Settings className="h-5 w-5 text-white" />
              </div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
                ConstructionSync
              </h1>
            </Link>
            <div className="flex items-center space-x-4">
              <Link to="/quotations" className="text-muted-foreground hover:text-accent transition-colors">
                Quotation Builder
              </Link>
              <Link to="/contracts" className="text-muted-foreground hover:text-primary transition-colors">
                Contract Builder
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-12">
        <div className="max-w-md mx-auto">
          {/* Back Button */}
          <Button variant="ghost" asChild className="mb-6">
            <Link to="/contracts" className="flex items-center">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Contract Builder
            </Link>
          </Button>

          {/* Login Card */}
          <Card className="border-2 border-primary/10">
            <CardHeader className="text-center space-y-4">
              <div className="mx-auto p-4 bg-primary/10 rounded-xl text-primary w-fit">
                <FileText className="h-8 w-8" />
              </div>
              <div>
                <Badge className="mb-2 bg-primary/10 text-primary border-primary/20">
                  Contract Builder
                </Badge>
                <CardTitle className="text-2xl">Sign In to Your Account</CardTitle>
                <CardDescription>
                  Access your contract dashboard to manage all your contracts, track status, and collaborate with clients
                </CardDescription>
              </div>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-4">
                {error && (
                  <Alert variant="destructive">
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}
                
                <div className="space-y-2">
                  <Label htmlFor="email">Email Address</Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    required
                    disabled={isLoading}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="password">Password</Label>
                  <Input
                    id="password"
                    type="password"
                    placeholder="Enter your password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    required
                    disabled={isLoading}
                  />
                </div>

                <Button 
                  type="submit" 
                  className="w-full bg-primary hover:bg-primary/90" 
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Signing In...
                    </>
                  ) : (
                    "Sign In"
                  )}
                </Button>
              </form>

              <div className="mt-6 text-center space-y-4">
                <div className="text-sm text-muted-foreground">
                  Don't have a Contract Builder account?
                </div>
                <Button variant="outline" asChild className="w-full border-primary text-primary hover:bg-primary hover:text-white">
                  <Link to="/contract-register">
                    Create Contract Account
                  </Link>
                </Button>
              </div>

              {/* Demo Credentials */}
              <div className="mt-6 p-4 bg-muted/50 rounded-lg">
                <h4 className="font-semibold mb-2 text-sm">Demo Credentials:</h4>
                <div className="text-xs text-muted-foreground space-y-1">
                  <div>Email: <EMAIL></div>
                  <div>Password: any password</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Features */}
          <div className="mt-8 text-center">
            <h3 className="font-semibold mb-4">Contract Builder Features</h3>
            <div className="grid grid-cols-1 gap-3 text-sm text-muted-foreground">
              <div className="flex items-center justify-center space-x-2">
                <div className="w-2 h-2 bg-primary rounded-full"></div>
                <span>Track all your contracts in one dashboard</span>
              </div>
              <div className="flex items-center justify-center space-x-2">
                <div className="w-2 h-2 bg-primary rounded-full"></div>
                <span>Monitor contract status and client responses</span>
              </div>
              <div className="flex items-center justify-center space-x-2">
                <div className="w-2 h-2 bg-primary rounded-full"></div>
                <span>Edit and resend contracts easily</span>
              </div>
              <div className="flex items-center justify-center space-x-2">
                <div className="w-2 h-2 bg-primary rounded-full"></div>
                <span>Professional templates and e-signatures</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContractLogin;
