import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Building2, 
  FileText, 
  Calculator, 
  ArrowRight, 
  CheckCircle,
  Users,
  BarChart3,
  Settings
} from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";

const ModuleSelection = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [selectedModule, setSelectedModule] = useState<string | null>(null);

  const modules = [
    {
      id: "construction-platform",
      title: "ConstructionSync Platform",
      description: "Complete project management solution for construction teams",
      icon: Building2,
      features: [
        "Project Dashboard & Analytics",
        "Client Portal Management", 
        "Progress Tracking",
        "Budget Management",
        "Team Collaboration",
        "Document Management"
      ],
      route: "/dashboard",
      color: "bg-blue-500",
      hoverColor: "hover:bg-blue-600"
    },
    {
      id: "quotation-builder",
      title: "Quotation Builder",
      description: "Professional quotation creation and management system",
      icon: Calculator,
      features: [
        "Dynamic Quote Creation",
        "Template Management",
        "Client Approval Workflow",
        "Cost Calculations",
        "PDF Generation",
        "Quote Tracking"
      ],
      route: "/quotation-dashboard",
      color: "bg-green-500",
      hoverColor: "hover:bg-green-600"
    },
    {
      id: "contract-builder",
      title: "Contract Builder",
      description: "Streamlined contract creation and management platform",
      icon: FileText,
      features: [
        "Contract Templates",
        "Digital Signatures",
        "Terms Management",
        "Client Collaboration",
        "Version Control",
        "Legal Compliance"
      ],
      route: "/contract-dashboard",
      color: "bg-purple-500",
      hoverColor: "hover:bg-purple-600"
    }
  ];

  const handleModuleSelect = (moduleId: string, route: string) => {
    setSelectedModule(moduleId);
    // Add a small delay for visual feedback
    setTimeout(() => {
      navigate(route);
    }, 300);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center mb-6">
            <Building2 className="h-12 w-12 text-blue-600 mr-3" />
            <h1 className="text-4xl font-bold text-gray-900">Construction Sync Hub</h1>
          </div>
          <p className="text-xl text-gray-600 mb-4">
            Welcome back, {user?.user_metadata?.full_name || user?.email}!
          </p>
          <p className="text-lg text-gray-500">
            Choose the module you'd like to access. You can switch between modules anytime.
          </p>
        </div>

        {/* Module Cards */}
        <div className="grid md:grid-cols-3 gap-8 max-w-7xl mx-auto">
          {modules.map((module) => {
            const IconComponent = module.icon;
            const isSelected = selectedModule === module.id;
            
            return (
              <Card 
                key={module.id}
                className={`relative overflow-hidden transition-all duration-300 cursor-pointer transform hover:scale-105 hover:shadow-xl ${
                  isSelected ? 'ring-4 ring-blue-500 shadow-xl scale-105' : 'hover:shadow-lg'
                }`}
                onClick={() => handleModuleSelect(module.id, module.route)}
              >
                <CardHeader className="pb-4">
                  <div className="flex items-center justify-between mb-4">
                    <div className={`p-3 rounded-lg ${module.color} text-white`}>
                      <IconComponent className="h-8 w-8" />
                    </div>
                    {isSelected && (
                      <CheckCircle className="h-6 w-6 text-green-500 animate-pulse" />
                    )}
                  </div>
                  <CardTitle className="text-xl font-bold text-gray-900">
                    {module.title}
                  </CardTitle>
                  <CardDescription className="text-gray-600">
                    {module.description}
                  </CardDescription>
                </CardHeader>
                
                <CardContent>
                  <div className="space-y-3 mb-6">
                    {module.features.map((feature, index) => (
                      <div key={index} className="flex items-center text-sm text-gray-600">
                        <CheckCircle className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                        {feature}
                      </div>
                    ))}
                  </div>
                  
                  <Button 
                    className={`w-full ${module.color} ${module.hoverColor} text-white transition-colors`}
                    disabled={isSelected}
                  >
                    {isSelected ? (
                      <>
                        <CheckCircle className="mr-2 h-4 w-4" />
                        Opening...
                      </>
                    ) : (
                      <>
                        Access Module
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </>
                    )}
                  </Button>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Quick Stats */}
        <div className="mt-16 text-center">
          <h3 className="text-lg font-semibold text-gray-700 mb-6">
            All modules are integrated with your unified account
          </h3>
          <div className="flex justify-center space-x-8 text-sm text-gray-500">
            <div className="flex items-center">
              <Users className="h-4 w-4 mr-2" />
              Single Sign-On
            </div>
            <div className="flex items-center">
              <BarChart3 className="h-4 w-4 mr-2" />
              Unified Analytics
            </div>
            <div className="flex items-center">
              <Settings className="h-4 w-4 mr-2" />
              Cross-Module Access
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ModuleSelection;
