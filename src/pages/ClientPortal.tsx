
import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Button } from "@/components/ui/button";
import { Calendar, Users, FileText, Settings, Clock, File, MessageCircle, AlertTriangle, DollarSign } from "lucide-react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { useParams } from "react-router-dom";
import ClientCommentSystem from "@/components/ClientCommentSystem";
import { ClientComment, ClientCommentResponse, createClientComment } from "@/types/project";

const ClientPortal = () => {
  const { token } = useParams();

  // State for client comments
  const [clientComments, setClientComments] = useState<ClientComment[]>([
    // Sample comment data
    createClientComment(
      "project-1",
      "The progress looks great! I'm particularly impressed with the quality of the foundation work. When do you expect to start the interior work?",
      "<PERSON>",
      "<EMAIL>",
      "question",
      "medium"
    ),
    {
      ...createClientComment(
        "project-1",
        "I have some concerns about the timeline. The weather delays seem to be impacting our original schedule. Can we discuss potential mitigation strategies?",
        "John Sterling",
        "<EMAIL>",
        "concern",
        "high"
      ),
      responses: [{
        id: "response_1",
        content: "Thank you for bringing this up. We've already adjusted our schedule to account for weather delays and have additional crews on standby. We'll have a detailed timeline update ready by tomorrow.",
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        responderId: "contractor_1",
        responderName: "Mike Johnson",
        responderRole: "project_manager" as const,
        isClientVisible: true
      }],
      status: "responded" as const
    }
  ]);
  
  // Mock project data that would normally be fetched using the token
  const projectData = {
    name: "Downtown Office Complex",
    client: "Sterling Corp",
    contractor: "BuildRight Construction",
    progress: 75,
    budget: 250000,
    spent: 185000,
    status: "active",
    startDate: "Sep 1, 2025",
    dueDate: "Dec 15, 2025",
    lastUpdate: "2 hours ago",
    milestones: [
      { name: "Foundation", completed: true, date: "Sep 15, 2025" },
      { name: "Framing", completed: true, date: "Oct 10, 2025" },
      { name: "Roofing", completed: true, date: "Nov 1, 2025" },
      { name: "Interior Work", completed: false, date: "Nov 30, 2025" },
      { name: "Final Inspection", completed: false, date: "Dec 10, 2025" }
    ],
    recentUpdates: [
      {
        date: "2 hours ago",
        update: "Electrical wiring completed for 3rd floor",
        type: "progress",
        priority: "normal"
      },
      {
        date: "1 day ago",
        update: "New materials delivered: 200 steel reinforcement bars",
        type: "materials",
        priority: "normal"
      },
      {
        date: "3 days ago",
        update: "Plumbing inspection passed successfully",
        type: "milestone",
        priority: "high"
      },
      {
        date: "5 hours ago",
        update: "Weather delay: Rain postponed exterior work until tomorrow",
        type: "alert",
        priority: "medium"
      },
      {
        date: "1 day ago",
        update: "Client comment received: Question about timeline",
        type: "comment",
        priority: "medium"
      },
      {
        date: "2 days ago",
        update: "Budget update: 75% of allocated funds utilized",
        type: "budget",
        priority: "normal"
      }
    ],
    contracts: [
      {
        id: "CNT001",
        title: "Main Construction Contract",
        type: "contract",
        status: "signed",
        totalCost: 250000,
        createdDate: "Nov 1, 2025",
        signedDate: "Nov 5, 2025",
        requiresAction: false
      },
      {
        id: "QUO001", 
        title: "Electrical Installation Quote",
        type: "quotation",
        status: "pending-approval",
        totalCost: 45000,
        createdDate: "Nov 10, 2025",
        signedDate: null,
        requiresAction: true
      }
    ]
  };

  const completedMilestones = projectData.milestones.filter(m => m.completed).length;
  const budgetPercentage = (projectData.spent / projectData.budget) * 100;
  const remainingBudget = projectData.budget - projectData.spent;

  // Comment handling functions
  const handleAddComment = (comment: ClientComment) => {
    setClientComments(prev => [comment, ...prev]);
  };

  const handleAddResponse = (commentId: string, response: ClientCommentResponse) => {
    setClientComments(prev =>
      prev.map(comment =>
        comment.id === commentId
          ? { ...comment, responses: [...(comment.responses || []), response], status: 'responded' as const }
          : comment
      )
    );
  };

  const handleUpdateCommentStatus = (commentId: string, status: ClientComment['status']) => {
    setClientComments(prev =>
      prev.map(comment =>
        comment.id === commentId
          ? { ...comment, status }
          : comment
      )
    );
  };

  const getContractStatusColor = (status: string) => {
    switch (status) {
      case "signed": return "bg-green-500";
      case "pending-approval": return "bg-yellow-500";
      case "awaiting-signature": return "bg-blue-500";
      default: return "bg-gray-500";
    }
  };

  const getContractStatusText = (status: string) => {
    return status.split('-').map(word =>
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  const getUpdateTypeIcon = (type: string) => {
    switch (type) {
      case "progress": return <Clock className="h-4 w-4 text-blue-500" />;
      case "materials": return <FileText className="h-4 w-4 text-green-500" />;
      case "milestone": return <Calendar className="h-4 w-4 text-purple-500" />;
      case "alert": return <AlertTriangle className="h-4 w-4 text-orange-500" />;
      case "comment": return <MessageCircle className="h-4 w-4 text-indigo-500" />;
      case "budget": return <DollarSign className="h-4 w-4 text-emerald-500" />;
      default: return <FileText className="h-4 w-4 text-gray-500" />;
    }
  };

  const getUpdatePriorityColor = (priority: string) => {
    switch (priority) {
      case "high": return "border-l-red-500 bg-red-50";
      case "medium": return "border-l-yellow-500 bg-yellow-50";
      case "normal": return "border-l-blue-500 bg-blue-50";
      default: return "border-l-gray-500 bg-gray-50";
    }
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Client Portal Header */}
      <header className="border-b bg-white/90 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="h-10 w-10 construction-gradient rounded-lg flex items-center justify-center">
                <Settings className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold">Client Project Portal</h1>
                <p className="text-sm text-muted-foreground">
                  Real-time project updates • Access ID: {token}
                </p>
              </div>
            </div>
            <Badge variant="default" className="bg-green-500">
              <div className="w-2 h-2 bg-white rounded-full mr-2"></div>
              Live Updates
            </Badge>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        {/* Project Overview */}
        <div className="mb-8">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
            <div>
              <h2 className="text-3xl font-bold mb-2">{projectData.name}</h2>
              <div className="flex items-center space-x-4 text-muted-foreground">
                <span>Contractor: {projectData.contractor}</span>
                <span>•</span>
                <span>Started: {projectData.startDate}</span>
                <span>•</span>
                <span>Due: {projectData.dueDate}</span>
              </div>
            </div>
            <div className="flex flex-col items-end space-y-2 mt-4 md:mt-0">
              <div className="flex items-center space-x-2">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm text-muted-foreground">Last updated {projectData.lastUpdate}</span>
              </div>
              {clientComments.filter(c => c.status === 'new').length > 0 && (
                <Badge className="bg-blue-500 text-white text-xs">
                  {clientComments.filter(c => c.status === 'new').length} New Response{clientComments.filter(c => c.status === 'new').length > 1 ? 's' : ''}
                </Badge>
              )}
            </div>
          </div>

          {/* Real-time High Priority Notifications */}
          {projectData.recentUpdates.slice(0, 1).map((update, index) => (
            update.priority === 'high' && (
              <div key={index} className="mb-4 p-4 bg-orange-50 border border-orange-200 rounded-lg">
                <div className="flex items-center space-x-2">
                  <AlertTriangle className="h-5 w-5 text-orange-500" />
                  <span className="font-medium text-orange-700">Latest High Priority Update</span>
                  <Badge className="bg-orange-500 text-white text-xs">
                    {update.date}
                  </Badge>
                </div>
                <p className="text-sm text-orange-600 mt-2">{update.update}</p>
              </div>
            )
          ))}
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Card className="border-2 border-primary/20">
            <CardHeader className="text-center">
              <CardTitle className="text-lg">Project Progress</CardTitle>
              <div className="text-4xl font-bold text-primary mb-2">{projectData.progress}%</div>
            </CardHeader>
            <CardContent>
              <Progress value={projectData.progress} className="mb-4" />
              <div className="flex justify-between text-sm text-muted-foreground">
                <span>Started {projectData.startDate}</span>
                <span>Due {projectData.dueDate}</span>
              </div>
            </CardContent>
          </Card>

          <Card className="border-2 border-accent/20">
            <CardHeader className="text-center">
              <CardTitle className="text-lg">Budget Status</CardTitle>
              <div className="text-4xl font-bold text-accent mb-2">
                ${(projectData.spent / 1000).toFixed(0)}K
              </div>
            </CardHeader>
            <CardContent>
              <Progress value={budgetPercentage} className="mb-4" />
              <div className="flex justify-between text-sm text-muted-foreground">
                <span>Spent: ${(projectData.spent / 1000).toFixed(0)}K</span>
                <span>Budget: ${(projectData.budget / 1000).toFixed(0)}K</span>
              </div>
              <p className="text-center text-sm mt-2">
                <span className="text-green-600 font-medium">
                  ${(remainingBudget / 1000).toFixed(0)}K remaining
                </span>
              </p>
            </CardContent>
          </Card>

          <Card className="border-2 border-green-500/20">
            <CardHeader className="text-center">
              <CardTitle className="text-lg">Milestones</CardTitle>
              <div className="text-4xl font-bold text-green-600 mb-2">
                {completedMilestones}/{projectData.milestones.length}
              </div>
            </CardHeader>
            <CardContent>
              <Progress 
                value={(completedMilestones / projectData.milestones.length) * 100} 
                className="mb-4" 
              />
              <p className="text-center text-sm text-muted-foreground">
                {projectData.milestones.length - completedMilestones} milestones remaining
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Tabbed Content */}
        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="contracts">Contracts</TabsTrigger>
            <TabsTrigger value="milestones">Milestones</TabsTrigger>
            <TabsTrigger value="updates">Updates</TabsTrigger>
            <TabsTrigger value="comments" className="relative">
              <MessageCircle className="mr-2 h-4 w-4" />
              Comments
              {clientComments.filter(c => c.status === 'new').length > 0 && (
                <Badge className="ml-2 h-5 w-5 p-0 text-xs bg-red-500">
                  {clientComments.filter(c => c.status === 'new').length}
                </Badge>
              )}
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid lg:grid-cols-2 gap-8">
              {/* Project Milestones */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Calendar className="mr-2 h-5 w-5" />
                    Project Milestones
                  </CardTitle>
                  <CardDescription>Key project phases and completion status</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {projectData.milestones.slice(0, 3).map((milestone, index) => (
                      <div key={index} className="flex items-center space-x-3">
                        <div className={`w-3 h-3 rounded-full ${
                          milestone.completed ? 'bg-green-500' : 'bg-gray-300'
                        }`}></div>
                        <div className="flex-1">
                          <div className="flex justify-between items-center">
                            <span className={`font-medium ${
                              milestone.completed ? 'text-green-700' : 'text-muted-foreground'
                            }`}>
                              {milestone.name}
                            </span>
                            <span className="text-sm text-muted-foreground">
                              {milestone.date}
                            </span>
                          </div>
                        </div>
                        {milestone.completed && (
                          <Badge variant="secondary" className="bg-green-100 text-green-700">
                            Complete
                          </Badge>
                        )}
                      </div>
                    ))}
                    <Button variant="outline" className="w-full mt-4">
                      View All Milestones
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Recent Updates */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <FileText className="mr-2 h-5 w-5" />
                    Recent Updates
                  </CardTitle>
                  <CardDescription>Latest project activity and progress updates</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {projectData.recentUpdates.map((update, index) => (
                      <div key={index} className="border-l-2 border-primary/20 pl-4">
                        <div className="flex items-center justify-between mb-1">
                          <Badge variant="outline" className="text-xs">
                            {update.type}
                          </Badge>
                          <span className="text-xs text-muted-foreground">
                            {update.date}
                          </span>
                        </div>
                        <p className="text-sm">{update.update}</p>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="contracts">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <File className="mr-2 h-5 w-5" />
                  Contracts & Quotations
                </CardTitle>
                <CardDescription>
                  Review, approve, and sign project contracts and quotations
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {projectData.contracts.map((contract) => (
                    <div key={contract.id} className="border rounded-lg p-4">
                      <div className="flex flex-col md:flex-row justify-between items-start md:items-center space-y-2 md:space-y-0">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-1">
                            <FileText className="h-4 w-4 text-muted-foreground" />
                            <h3 className="font-semibold">{contract.title}</h3>
                            <Badge variant="outline" className="text-xs">
                              {contract.type}
                            </Badge>
                            {contract.requiresAction && (
                              <Badge className="bg-red-500 text-white text-xs">
                                Action Required
                              </Badge>
                            )}
                          </div>
                          <p className="text-sm text-muted-foreground mb-1">
                            Created: {contract.createdDate}
                            {contract.signedDate && ` • Signed: ${contract.signedDate}`}
                          </p>
                          <p className="text-sm font-medium">
                            Total: ${contract.totalCost.toLocaleString()}
                          </p>
                        </div>
                        
                        <div className="flex items-center space-x-2">
                          <Badge className={`${getContractStatusColor(contract.status)} text-white`}>
                            {getContractStatusText(contract.status)}
                          </Badge>
                          <Button variant="outline" size="sm">
                            View Document
                          </Button>
                          {contract.status === "pending-approval" && (
                            <Button size="sm">
                              Review & Approve
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="milestones">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Calendar className="mr-2 h-5 w-5" />
                  All Project Milestones
                </CardTitle>
                <CardDescription>Complete list of project phases and completion status</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {projectData.milestones.map((milestone, index) => (
                    <div key={index} className="flex items-center space-x-3">
                      <div className={`w-3 h-3 rounded-full ${
                        milestone.completed ? 'bg-green-500' : 'bg-gray-300'
                      }`}></div>
                      <div className="flex-1">
                        <div className="flex justify-between items-center">
                          <span className={`font-medium ${
                            milestone.completed ? 'text-green-700' : 'text-muted-foreground'
                          }`}>
                            {milestone.name}
                          </span>
                          <span className="text-sm text-muted-foreground">
                            {milestone.date}
                          </span>
                        </div>
                      </div>
                      {milestone.completed && (
                        <Badge variant="secondary" className="bg-green-100 text-green-700">
                          Complete
                        </Badge>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="updates">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <FileText className="mr-2 h-5 w-5" />
                  All Recent Updates
                </CardTitle>
                <CardDescription>Complete activity timeline for your project</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {projectData.recentUpdates.map((update, index) => (
                    <div
                      key={index}
                      className={`border-l-4 pl-4 py-3 rounded-r-lg ${getUpdatePriorityColor(update.priority)}`}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          {getUpdateTypeIcon(update.type)}
                          <Badge variant="outline" className="text-xs capitalize">
                            {update.type}
                          </Badge>
                          {update.priority === 'high' && (
                            <Badge className="bg-red-500 text-white text-xs">
                              High Priority
                            </Badge>
                          )}
                        </div>
                        <span className="text-xs text-muted-foreground font-medium">
                          {update.date}
                        </span>
                      </div>
                      <p className="text-sm leading-relaxed">{update.update}</p>
                    </div>
                  ))}
                </div>

                {/* Live Updates Indicator */}
                <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                    <span className="text-sm font-medium text-green-700">Live Updates Active</span>
                  </div>
                  <p className="text-xs text-green-600 mt-1">
                    This page automatically refreshes with new project updates every 30 seconds
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="comments">
            <ClientCommentSystem
              projectId={projectData.name}
              comments={clientComments}
              onAddComment={handleAddComment}
              onAddResponse={handleAddResponse}
              onUpdateCommentStatus={handleUpdateCommentStatus}
              isClientView={true}
              currentUserName="John Sterling"
              currentUserEmail="<EMAIL>"
              currentUserId="client_1"
            />
          </TabsContent>
        </Tabs>

        {/* Contact Information */}
        <Card className="mt-8 bg-muted/30">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="mr-2 h-5 w-5" />
              Project Contact
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
              <div>
                <h3 className="font-semibold">{projectData.contractor}</h3>
                <p className="text-muted-foreground">General Contractor</p>
                <p className="text-sm text-muted-foreground mt-1">
                  For questions about your project, contact your contractor directly.
                </p>
              </div>
              <Button className="mt-4 md:mt-0">
                Contact Contractor
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Footer Notice */}
        <div className="mt-8 text-center text-sm text-muted-foreground">
          <p>
            This portal provides real-time access to your construction project. 
            All data syncs automatically from the contractor dashboard.
          </p>
          <p className="mt-1">
            ConstructionSync Platform • Secure Client Access
          </p>
        </div>
      </div>
    </div>
  );
};

export default ClientPortal;
