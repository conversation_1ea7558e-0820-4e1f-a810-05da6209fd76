import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  ArrowLeft,
  ArrowRight,
  Plus,
  Trash2,
  Building,
  Users,
  Calendar,
  DollarSign,
  CheckCircle,
  Save,
  AlertTriangle
} from "lucide-react";
import DashboardLayout from "@/components/DashboardLayout";
import { useAuth } from "@/contexts/AuthContext";
import { useProjects } from "@/contexts/ProjectContext";
import {
  ProjectFormData,
  validateProjectFormData,
  convertFormDataToProject,
  ProjectPhase,
  TeamMember
} from "@/types/project";

const NewProject = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { createProject } = useProjects();
  const [currentStep, setCurrentStep] = useState(1);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const [formData, setFormData] = useState<ProjectFormData>({
    name: "",
    description: "",
    client: "",
    contractor: "",
    startDate: "",
    dueDate: "",
    budget: 0,
    projectType: "",
    priority: "medium",
    location: "",
    phases: [],
    team: []
  });

  const [newPhase, setNewPhase] = useState<Omit<ProjectPhase, 'id' | 'progress' | 'status' | 'spent' | 'actualStartDate' | 'actualEndDate'>>({
    name: "",
    description: "",
    budget: 0,
    duration: 0,
    dependencies: [],
    dueDate: ""
  });

  const [newTeamMember, setNewTeamMember] = useState<Omit<TeamMember, 'id' | 'isActive' | 'joinedDate'>>({
    name: "",
    email: "",
    role: "",
    phone: ""
  });

  const totalSteps = 4;
  const progressPercentage = (currentStep / totalSteps) * 100;

  const handleInputChange = (field: keyof ProjectFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const addPhase = () => {
    if (newPhase.name && newPhase.budget > 0 && newPhase.duration > 0 && newPhase.dueDate) {
      const phase = {
        ...newPhase,
        id: Date.now().toString()
      };
      setFormData(prev => ({
        ...prev,
        phases: [...prev.phases, phase]
      }));
      setNewPhase({
        name: "",
        description: "",
        budget: 0,
        duration: 0,
        dependencies: [],
        dueDate: ""
      });
    }
  };

  const removePhase = (phaseId: string) => {
    setFormData(prev => ({
      ...prev,
      phases: prev.phases.filter(p => p.id !== phaseId)
    }));
  };

  const addTeamMember = () => {
    if (newTeamMember.name && newTeamMember.email && newTeamMember.role) {
      const member: TeamMember = {
        ...newTeamMember,
        id: Date.now().toString()
      };
      setFormData(prev => ({
        ...prev,
        team: [...prev.team, member]
      }));
      setNewTeamMember({
        name: "",
        email: "",
        role: "",
        phone: ""
      });
    }
  };

  const removeTeamMember = (memberId: string) => {
    setFormData(prev => ({
      ...prev,
      team: prev.team.filter(m => m.id !== memberId)
    }));
  };

  const nextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = async () => {
    if (!user) return;

    setIsSubmitting(true);
    setValidationErrors([]);

    // Validate form data
    const errors = validateProjectFormData(formData);
    if (errors.length > 0) {
      setValidationErrors(errors);
      setIsSubmitting(false);
      return;
    }

    try {
      // Create project using the database
      const projectData = {
        name: formData.name,
        description: formData.description,
        status: 'active',
        progress: 0,
        start_date: formData.startDate || null,
        end_date: formData.dueDate || null,
        budget: formData.budget,
        spent: 0,
        location: formData.location || null,
        project_type: formData.projectType || null,
        priority: formData.priority || 'medium',
        client_portal_enabled: false,
        client_portal_token: null
      };

      const newProject = await createProject(projectData);

      if (newProject) {
        console.log("Project created successfully:", newProject);
        navigate("/dashboard");
      } else {
        setValidationErrors(["Failed to create project. Please try again."]);
      }
    } catch (error) {
      console.error("Error creating project:", error);
      setValidationErrors(["Failed to create project. Please try again."]);
    } finally {
      setIsSubmitting(false);
    }
  };

  const isStepValid = (step: number) => {
    switch (step) {
      case 1:
        return formData.name && formData.client && formData.contractor && formData.description;
      case 2:
        return formData.startDate && formData.dueDate && formData.budget > 0;
      case 3:
        return formData.phases.length > 0;
      case 4:
        return formData.team.length > 0;
      default:
        return false;
    }
  };

  const canProceed = isStepValid(currentStep);

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" onClick={() => navigate("/dashboard")}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Dashboard
            </Button>
            <div>
              <h1 className="text-3xl font-bold">Create New Project</h1>
              <p className="text-muted-foreground">
                Set up your construction project with all the essential details
              </p>
            </div>
          </div>
        </div>

        {/* Progress Indicator */}
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle className="text-lg">Project Setup Progress</CardTitle>
              <Badge variant="outline">
                Step {currentStep} of {totalSteps}
              </Badge>
            </div>
            <Progress value={progressPercentage} className="mt-2" />
          </CardHeader>
        </Card>

        {/* Validation Errors */}
        {validationErrors.length > 0 && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              <div className="space-y-1">
                <p className="font-medium">Please fix the following errors:</p>
                <ul className="list-disc list-inside space-y-1">
                  {validationErrors.map((error, index) => (
                    <li key={index} className="text-sm">{error}</li>
                  ))}
                </ul>
              </div>
            </AlertDescription>
          </Alert>
        )}

        {/* Step Content */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Step Navigation */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Setup Steps</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {[
                  { step: 1, title: "Basic Information", icon: Building },
                  { step: 2, title: "Timeline & Budget", icon: Calendar },
                  { step: 3, title: "Project Phases", icon: DollarSign },
                  { step: 4, title: "Team Members", icon: Users }
                ].map(({ step, title, icon: Icon }) => (
                  <div
                    key={step}
                    className={`flex items-center space-x-3 p-3 rounded-lg transition-colors ${
                      currentStep === step
                        ? "bg-primary text-primary-foreground"
                        : currentStep > step
                        ? "bg-green-100 text-green-800"
                        : "bg-muted text-muted-foreground"
                    }`}
                  >
                    {currentStep > step ? (
                      <CheckCircle className="h-5 w-5" />
                    ) : (
                      <Icon className="h-5 w-5" />
                    )}
                    <span className="font-medium">{title}</span>
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            <Card>
              <CardHeader>
                <CardTitle>
                  {currentStep === 1 && "Basic Project Information"}
                  {currentStep === 2 && "Timeline & Budget Details"}
                  {currentStep === 3 && "Project Phases"}
                  {currentStep === 4 && "Team Members"}
                </CardTitle>
                <CardDescription>
                  {currentStep === 1 && "Enter the fundamental details about your construction project"}
                  {currentStep === 2 && "Set project timeline and budget parameters"}
                  {currentStep === 3 && "Define the phases and milestones for your project"}
                  {currentStep === 4 && "Add team members and assign their roles"}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Step 1: Basic Information */}
                {currentStep === 1 && (
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <Label htmlFor="projectName">Project Name *</Label>
                        <Input
                          id="projectName"
                          placeholder="e.g., Downtown Office Complex"
                          value={formData.name}
                          onChange={(e) => handleInputChange("name", e.target.value)}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="client">Client Name *</Label>
                        <Input
                          id="client"
                          placeholder="e.g., Sterling Corp"
                          value={formData.client}
                          onChange={(e) => handleInputChange("client", e.target.value)}
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <Label htmlFor="contractor">Contractor *</Label>
                        <Input
                          id="contractor"
                          placeholder="e.g., BuildRight Construction"
                          value={formData.contractor}
                          onChange={(e) => handleInputChange("contractor", e.target.value)}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="location">Project Location</Label>
                        <Input
                          id="location"
                          placeholder="e.g., 123 Main St, City, State"
                          value={formData.location}
                          onChange={(e) => handleInputChange("location", e.target.value)}
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <Label htmlFor="projectType">Project Type</Label>
                        <Select
                          value={formData.projectType}
                          onValueChange={(value) => handleInputChange("projectType", value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select project type" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="commercial">Commercial Building</SelectItem>
                            <SelectItem value="residential">Residential</SelectItem>
                            <SelectItem value="industrial">Industrial</SelectItem>
                            <SelectItem value="infrastructure">Infrastructure</SelectItem>
                            <SelectItem value="renovation">Renovation</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="priority">Priority Level</Label>
                        <Select
                          value={formData.priority}
                          onValueChange={(value) => handleInputChange("priority", value)}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="low">Low</SelectItem>
                            <SelectItem value="medium">Medium</SelectItem>
                            <SelectItem value="high">High</SelectItem>
                            <SelectItem value="urgent">Urgent</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="description">Project Description *</Label>
                      <Textarea
                        id="description"
                        placeholder="Provide a detailed description of the project scope, objectives, and key requirements..."
                        rows={4}
                        value={formData.description}
                        onChange={(e) => handleInputChange("description", e.target.value)}
                      />
                    </div>
                  </div>
                )}

                {/* Step 2: Timeline & Budget */}
                {currentStep === 2 && (
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <Label htmlFor="startDate">Project Start Date *</Label>
                        <Input
                          id="startDate"
                          type="date"
                          value={formData.startDate}
                          onChange={(e) => handleInputChange("startDate", e.target.value)}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="dueDate">Expected Completion Date *</Label>
                        <Input
                          id="dueDate"
                          type="date"
                          value={formData.dueDate}
                          onChange={(e) => handleInputChange("dueDate", e.target.value)}
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="budget">Total Project Budget *</Label>
                      <div className="relative">
                        <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">$</span>
                        <Input
                          id="budget"
                          type="number"
                          placeholder="250000"
                          className="pl-8"
                          value={formData.budget || ""}
                          onChange={(e) => handleInputChange("budget", parseFloat(e.target.value) || 0)}
                        />
                      </div>
                      <p className="text-sm text-muted-foreground">
                        Enter the total budget for the entire project
                      </p>
                    </div>

                    {formData.startDate && formData.dueDate && (
                      <Card className="bg-muted/50">
                        <CardContent className="pt-6">
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                            <div>
                              <p className="text-sm text-muted-foreground">Project Duration</p>
                              <p className="text-2xl font-bold">
                                {Math.ceil(
                                  (new Date(formData.dueDate).getTime() - new Date(formData.startDate).getTime()) /
                                  (1000 * 60 * 60 * 24)
                                )} days
                              </p>
                            </div>
                            <div>
                              <p className="text-sm text-muted-foreground">Budget per Day</p>
                              <p className="text-2xl font-bold">
                                ${formData.budget > 0 ? Math.round(
                                  formData.budget / Math.ceil(
                                    (new Date(formData.dueDate).getTime() - new Date(formData.startDate).getTime()) /
                                    (1000 * 60 * 60 * 24)
                                  )
                                ).toLocaleString() : '0'}
                              </p>
                            </div>
                            <div>
                              <p className="text-sm text-muted-foreground">Total Budget</p>
                              <p className="text-2xl font-bold text-primary">
                                ${formData.budget.toLocaleString()}
                              </p>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    )}
                  </div>
                )}

                {/* Step 3: Project Phases */}
                {currentStep === 3 && (
                  <div className="space-y-6">
                    {/* Add New Phase */}
                    <Card className="border-dashed">
                      <CardHeader>
                        <CardTitle className="text-lg">Add Project Phase</CardTitle>
                        <CardDescription>
                          Break down your project into manageable phases with individual budgets and timelines
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="phaseName">Phase Name</Label>
                            <Input
                              id="phaseName"
                              placeholder="e.g., Foundation"
                              value={newPhase.name}
                              onChange={(e) => setNewPhase(prev => ({ ...prev, name: e.target.value }))}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="phaseBudget">Phase Budget</Label>
                            <div className="relative">
                              <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">$</span>
                              <Input
                                id="phaseBudget"
                                type="number"
                                placeholder="45000"
                                className="pl-8"
                                value={newPhase.budget || ""}
                                onChange={(e) => setNewPhase(prev => ({ ...prev, budget: parseFloat(e.target.value) || 0 }))}
                              />
                            </div>
                          </div>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="phaseDuration">Duration (days)</Label>
                            <Input
                              id="phaseDuration"
                              type="number"
                              placeholder="30"
                              value={newPhase.duration || ""}
                              onChange={(e) => setNewPhase(prev => ({ ...prev, duration: parseInt(e.target.value) || 0 }))}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="phaseDueDate">Due Date</Label>
                            <Input
                              id="phaseDueDate"
                              type="date"
                              value={newPhase.dueDate}
                              onChange={(e) => setNewPhase(prev => ({ ...prev, dueDate: e.target.value }))}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="phaseDescription">Description</Label>
                            <Input
                              id="phaseDescription"
                              placeholder="Brief description of this phase"
                              value={newPhase.description}
                              onChange={(e) => setNewPhase(prev => ({ ...prev, description: e.target.value }))}
                            />
                          </div>
                        </div>
                        <Button onClick={addPhase} className="w-full">
                          <Plus className="mr-2 h-4 w-4" />
                          Add Phase
                        </Button>
                      </CardContent>
                    </Card>

                    {/* Existing Phases */}
                    {formData.phases.length > 0 && (
                      <div className="space-y-4">
                        <h3 className="text-lg font-semibold">Project Phases ({formData.phases.length})</h3>
                        {formData.phases.map((phase, index) => (
                          <Card key={phase.id}>
                            <CardContent className="pt-6">
                              <div className="flex justify-between items-start">
                                <div className="flex-1">
                                  <div className="flex items-center space-x-2 mb-2">
                                    <Badge variant="outline">Phase {index + 1}</Badge>
                                    <h4 className="font-semibold">{phase.name}</h4>
                                  </div>
                                  <p className="text-sm text-muted-foreground mb-3">{phase.description}</p>
                                  <div className="grid grid-cols-2 gap-4 text-sm">
                                    <div>
                                      <span className="text-muted-foreground">Budget: </span>
                                      <span className="font-medium">${phase.budget.toLocaleString()}</span>
                                    </div>
                                    <div>
                                      <span className="text-muted-foreground">Duration: </span>
                                      <span className="font-medium">{phase.duration} days</span>
                                    </div>
                                  </div>
                                </div>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => removePhase(phase.id)}
                                  className="text-destructive hover:text-destructive"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>
                            </CardContent>
                          </Card>
                        ))}

                        {/* Phase Summary */}
                        <Card className="bg-muted/50">
                          <CardContent className="pt-6">
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                              <div>
                                <p className="text-sm text-muted-foreground">Total Phases</p>
                                <p className="text-2xl font-bold">{formData.phases.length}</p>
                              </div>
                              <div>
                                <p className="text-sm text-muted-foreground">Total Phase Budget</p>
                                <p className="text-2xl font-bold">
                                  ${formData.phases.reduce((sum, phase) => sum + phase.budget, 0).toLocaleString()}
                                </p>
                              </div>
                              <div>
                                <p className="text-sm text-muted-foreground">Total Duration</p>
                                <p className="text-2xl font-bold">
                                  {formData.phases.reduce((sum, phase) => sum + phase.duration, 0)} days
                                </p>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      </div>
                    )}
                  </div>
                )}

                {/* Step 4: Team Members */}
                {currentStep === 4 && (
                  <div className="space-y-6">
                    {/* Add New Team Member */}
                    <Card className="border-dashed">
                      <CardHeader>
                        <CardTitle className="text-lg">Add Team Member</CardTitle>
                        <CardDescription>
                          Add key team members who will be working on this project
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="memberName">Full Name</Label>
                            <Input
                              id="memberName"
                              placeholder="e.g., Mike Johnson"
                              value={newTeamMember.name}
                              onChange={(e) => setNewTeamMember(prev => ({ ...prev, name: e.target.value }))}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="memberEmail">Email Address</Label>
                            <Input
                              id="memberEmail"
                              type="email"
                              placeholder="<EMAIL>"
                              value={newTeamMember.email}
                              onChange={(e) => setNewTeamMember(prev => ({ ...prev, email: e.target.value }))}
                            />
                          </div>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="memberRole">Role</Label>
                            <Select
                              value={newTeamMember.role}
                              onValueChange={(value) => setNewTeamMember(prev => ({ ...prev, role: value }))}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Select role" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="project-manager">Project Manager</SelectItem>
                                <SelectItem value="site-supervisor">Site Supervisor</SelectItem>
                                <SelectItem value="architect">Architect</SelectItem>
                                <SelectItem value="engineer">Engineer</SelectItem>
                                <SelectItem value="contractor">Contractor</SelectItem>
                                <SelectItem value="electrician">Electrician</SelectItem>
                                <SelectItem value="plumber">Plumber</SelectItem>
                                <SelectItem value="safety-officer">Safety Officer</SelectItem>
                                <SelectItem value="quality-inspector">Quality Inspector</SelectItem>
                                <SelectItem value="other">Other</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="memberPhone">Phone Number (Optional)</Label>
                            <Input
                              id="memberPhone"
                              placeholder="+****************"
                              value={newTeamMember.phone}
                              onChange={(e) => setNewTeamMember(prev => ({ ...prev, phone: e.target.value }))}
                            />
                          </div>
                        </div>
                        <Button onClick={addTeamMember} className="w-full">
                          <Plus className="mr-2 h-4 w-4" />
                          Add Team Member
                        </Button>
                      </CardContent>
                    </Card>

                    {/* Existing Team Members */}
                    {formData.team.length > 0 && (
                      <div className="space-y-4">
                        <h3 className="text-lg font-semibold">Team Members ({formData.team.length})</h3>
                        <div className="grid gap-4">
                          {formData.team.map((member) => (
                            <Card key={member.id}>
                              <CardContent className="pt-6">
                                <div className="flex justify-between items-start">
                                  <div className="flex items-start space-x-4">
                                    <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                                      <Users className="h-6 w-6 text-primary" />
                                    </div>
                                    <div>
                                      <h4 className="font-semibold">{member.name}</h4>
                                      <p className="text-sm text-muted-foreground capitalize">
                                        {member.role.replace('-', ' ')}
                                      </p>
                                      <p className="text-sm text-muted-foreground">{member.email}</p>
                                      {member.phone && (
                                        <p className="text-sm text-muted-foreground">{member.phone}</p>
                                      )}
                                    </div>
                                  </div>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => removeTeamMember(member.id)}
                                    className="text-destructive hover:text-destructive"
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </div>
                              </CardContent>
                            </Card>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </CardContent>

              {/* Navigation Buttons */}
              <div className="flex justify-between items-center p-6 border-t">
                <Button
                  variant="outline"
                  onClick={prevStep}
                  disabled={currentStep === 1}
                >
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Previous
                </Button>

                <div className="flex space-x-2">
                  {currentStep < totalSteps ? (
                    <Button
                      onClick={nextStep}
                      disabled={!canProceed}
                    >
                      Next
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  ) : (
                    <Button
                      onClick={handleSubmit}
                      disabled={!canProceed || isSubmitting}
                      className="bg-green-600 hover:bg-green-700"
                    >
                      <Save className="mr-2 h-4 w-4" />
                      {isSubmitting ? "Creating Project..." : "Create Project"}
                    </Button>
                  )}
                </div>
              </div>
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default NewProject;
