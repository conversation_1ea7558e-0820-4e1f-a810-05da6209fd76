import React, { useState } from 'react';
import ContractDashboardLayout from '@/components/ContractDashboardLayout';
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { 
  FileText, 
  Plus, 
  Send, 
  Copy, 
  Users, 
  Calendar, 
  CheckCircle,
  Building,
  DollarSign,
  ArrowRight,
  ArrowLeft,
  Clock,
  Edit,
  Eye,
  Share,
  Zap,
  Shield
} from "lucide-react";


interface ContractData {
  id?: string;
  title: string;
  clientName: string;
  clientEmail: string;
  clientPhone: string;
  projectType: string;
  projectAddress: string;
  startDate: string;
  endDate: string;
  totalAmount: string;
  description: string;
  scope: string;
  terms: string;
  paymentSchedule: string;
  materials: string;
  laborDetails: string;
  warranties: string;
  status: 'draft' | 'sent' | 'signed' | 'completed' | 'cancelled';
  createdAt: string;
  shareableLink?: string;
  // Customization options
  primaryColor?: string;
  backgroundColor?: string;
  textColor?: string;
  companyName?: string;
  companyLogo?: string;
}

// Color themes for customization
const colorThemes = [
  {
    name: "Professional Blue",
    primary: "#3b82f6",
    secondary: "#1e40af",
    accent: "#dbeafe",
    background: "#ffffff",
    text: "#1f2937"
  },
  {
    name: "Modern Green",
    primary: "#10b981",
    secondary: "#047857",
    accent: "#d1fae5",
    background: "#ffffff",
    text: "#1f2937"
  },
  {
    name: "Classic Navy",
    primary: "#1e3a8a",
    secondary: "#1e40af",
    accent: "#dbeafe",
    background: "#f8fafc",
    text: "#0f172a"
  },
  {
    name: "Elegant Purple",
    primary: "#7c3aed",
    secondary: "#5b21b6",
    accent: "#ede9fe",
    background: "#fefbff",
    text: "#1f2937"
  },
  {
    name: "Warm Orange",
    primary: "#ea580c",
    secondary: "#c2410c",
    accent: "#fed7aa",
    background: "#fffbeb",
    text: "#1c1917"
  },
  {
    name: "Sophisticated Gray",
    primary: "#374151",
    secondary: "#1f2937",
    accent: "#f3f4f6",
    background: "#ffffff",
    text: "#111827"
  },
  {
    name: "Vibrant Teal",
    primary: "#0d9488",
    secondary: "#0f766e",
    accent: "#ccfbf1",
    background: "#f0fdfa",
    text: "#134e4a"
  },
  {
    name: "Rich Burgundy",
    primary: "#be123c",
    secondary: "#9f1239",
    accent: "#fecdd3",
    background: "#fef2f2",
    text: "#1f2937"
  }
];

const NewContractBuilder = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [contractData, setContractData] = useState<ContractData>({
    title: "",
    clientName: "",
    clientEmail: "",
    clientPhone: "",
    projectType: "",
    projectAddress: "",
    startDate: "",
    endDate: "",
    totalAmount: "",
    description: "",
    scope: "",
    terms: "",
    paymentSchedule: "",
    materials: "",
    laborDetails: "",
    warranties: "",
    status: 'draft',
    createdAt: new Date().toISOString(),
    primaryColor: "#3b82f6",
    backgroundColor: "#ffffff",
    textColor: "#1f2937",
    companyName: "",
    companyLogo: ""
  });

  const handleInputChange = (field: keyof ContractData, value: string) => {
    setContractData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const applyTheme = (theme: typeof colorThemes[0]) => {
    setContractData(prev => ({
      ...prev,
      primaryColor: theme.primary,
      backgroundColor: theme.background,
      textColor: theme.text
    }));
  };

  const generateShareableLink = () => {
    const contractId = `contract-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const shareableLink = `${window.location.origin}/contract/${contractId}`;

    // Store contract data in localStorage for the shared link to access
    const contractWithId = {
      ...contractData,
      id: contractId,
      shareableLink,
      status: 'sent',
      createdAt: new Date().toISOString()
    };

    localStorage.setItem(`contract_${contractId}`, JSON.stringify(contractWithId));

    setContractData(prev => ({
      ...prev,
      id: contractId,
      shareableLink: shareableLink,
      status: 'sent'
    }));

    // Copy to clipboard
    navigator.clipboard.writeText(shareableLink).then(() => {
      alert(`✅ Contract Created Successfully!\n\n📋 Link copied to clipboard:\n${shareableLink}\n\n📧 Share this link with your client to review and sign the contract.`);
    });
  };

  const handleCreateContract = () => {
    if (!contractData.title || !contractData.clientName || !contractData.clientEmail) {
      alert('Please fill in all required fields (Title, Client Name, Client Email)');
      return;
    }
    
    generateShareableLink();
  };

  const nextStep = () => {
    if (currentStep < 6) setCurrentStep(currentStep + 1);
  };

  const prevStep = () => {
    if (currentStep > 1) setCurrentStep(currentStep - 1);
  };

  const getStepProgress = () => {
    return (currentStep / 6) * 100;
  };

  const handleLogoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setLogoFile(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        const logoDataUrl = e.target?.result as string;
        setContractData(prev => ({
          ...prev,
          companyLogo: logoDataUrl
        }));
      };
      reader.readAsDataURL(file);
    }
  };

  // Using shared template data

  const recentContracts: ContractData[] = [
    {
      id: "contract-1",
      title: "Kitchen Renovation Project",
      clientName: "John Smith",
      clientEmail: "<EMAIL>",
      clientPhone: "(*************",
      projectType: "renovation",
      projectAddress: "123 Main St, City, State",
      totalAmount: "25000",
      status: "signed",
      createdAt: "2024-01-15",
      description: "Complete kitchen renovation",
      scope: "Full kitchen remodel",
      terms: "Standard terms",
      paymentSchedule: "50% upfront, 50% completion",
      materials: "High-quality materials",
      laborDetails: "Professional team",
      warranties: "2-year warranty",
      startDate: "2024-02-01",
      endDate: "2024-03-15",
      shareableLink: `${window.location.origin}/contract/contract-1`
    },
    {
      id: "contract-2", 
      title: "Office Building Maintenance",
      clientName: "ABC Corporation",
      clientEmail: "<EMAIL>",
      clientPhone: "(*************",
      projectType: "maintenance",
      projectAddress: "456 Business Ave, City, State",
      totalAmount: "50000",
      status: "sent",
      createdAt: "2024-01-10",
      description: "Annual maintenance contract",
      scope: "Building maintenance",
      terms: "Annual contract",
      paymentSchedule: "Monthly payments",
      materials: "All materials included",
      laborDetails: "24/7 support",
      warranties: "1-year warranty",
      startDate: "2024-02-01",
      endDate: "2025-01-31",
      shareableLink: `${window.location.origin}/contract/contract-2`
    }
  ];

  const getStatusBadge = (status: string) => {
    const styles = {
      signed: "bg-green-100 text-green-800 border-green-300",
      sent: "bg-blue-100 text-blue-800 border-blue-300",
      draft: "bg-gray-100 text-gray-800 border-gray-300",
      completed: "bg-emerald-100 text-emerald-800 border-emerald-300",
      cancelled: "bg-red-100 text-red-800 border-red-300"
    };
    return styles[status as keyof typeof styles] || styles.draft;
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'signed': return <CheckCircle className="h-4 w-4" />;
      case 'sent': return <Send className="h-4 w-4" />;
      case 'draft': return <Edit className="h-4 w-4" />;
      case 'completed': return <CheckCircle className="h-4 w-4" />;
      case 'cancelled': return <Clock className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  return (
    <ContractDashboardLayout>
      <div className="p-4 sm:p-6 lg:p-8 max-w-7xl mx-auto">
        {/* Page Header */}
        <div className="mb-6 lg:mb-8">
          <h1 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-2">Contract Builder</h1>
          <p className="text-gray-600">Create professional contracts with digital signatures and shareable client links</p>
        </div>

        <Tabs defaultValue="create" className="w-full">
          <TabsList className="grid w-full grid-cols-2 mb-6">
            <TabsTrigger value="create" className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Create Contract
            </TabsTrigger>
            <TabsTrigger value="recent" className="flex items-center gap-2">
              <Clock className="h-4 w-4" />
              Recent Contracts
            </TabsTrigger>
          </TabsList>

          {/* Create Contract Tab */}
          <TabsContent value="create" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <Building className="h-5 w-5" />
                      New Contract
                    </CardTitle>
                    <CardDescription>
                      Step {currentStep} of 6 - Create a professional contract with digital signatures
                    </CardDescription>
                  </div>
                  <Badge variant="outline" className="text-sm">
                    {Math.round(getStepProgress())}% Complete
                  </Badge>
                </div>
                <Progress value={getStepProgress()} className="w-full mt-4" />
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Step 1: Basic Information */}
                {currentStep === 1 && (
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Basic Information</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="title">Contract Title *</Label>
                        <Input
                          id="title"
                          placeholder="e.g., Kitchen Renovation Contract"
                          value={contractData.title}
                          onChange={(e) => handleInputChange('title', e.target.value)}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="projectType">Project Type</Label>
                        <Select value={contractData.projectType} onValueChange={(value) => handleInputChange('projectType', value)}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select project type" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="renovation">Renovation</SelectItem>
                            <SelectItem value="new-construction">New Construction</SelectItem>
                            <SelectItem value="maintenance">Maintenance</SelectItem>
                            <SelectItem value="repair">Repair</SelectItem>
                            <SelectItem value="design">Design Services</SelectItem>
                            <SelectItem value="consultation">Consultation</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="startDate">Start Date</Label>
                        <Input
                          id="startDate"
                          type="date"
                          value={contractData.startDate}
                          onChange={(e) => handleInputChange('startDate', e.target.value)}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="endDate">Expected Completion Date</Label>
                        <Input
                          id="endDate"
                          type="date"
                          value={contractData.endDate}
                          onChange={(e) => handleInputChange('endDate', e.target.value)}
                        />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="description">Project Description</Label>
                      <Textarea
                        id="description"
                        placeholder="Describe the project scope and objectives..."
                        value={contractData.description}
                        onChange={(e) => handleInputChange('description', e.target.value)}
                        rows={3}
                      />
                    </div>
                  </div>
                )}

                {/* Step 2: Client Information */}
                {currentStep === 2 && (
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Client Information</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="clientName">Client Name *</Label>
                        <Input
                          id="clientName"
                          placeholder="Full name or company name"
                          value={contractData.clientName}
                          onChange={(e) => handleInputChange('clientName', e.target.value)}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="clientEmail">Client Email *</Label>
                        <Input
                          id="clientEmail"
                          type="email"
                          placeholder="<EMAIL>"
                          value={contractData.clientEmail}
                          onChange={(e) => handleInputChange('clientEmail', e.target.value)}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="clientPhone">Client Phone</Label>
                        <Input
                          id="clientPhone"
                          placeholder="(*************"
                          value={contractData.clientPhone}
                          onChange={(e) => handleInputChange('clientPhone', e.target.value)}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="totalAmount">Total Contract Amount ($)</Label>
                        <Input
                          id="totalAmount"
                          placeholder="25000"
                          value={contractData.totalAmount}
                          onChange={(e) => handleInputChange('totalAmount', e.target.value)}
                        />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="projectAddress">Project Address</Label>
                      <Textarea
                        id="projectAddress"
                        placeholder="Full project address including city, state, and zip code"
                        value={contractData.projectAddress}
                        onChange={(e) => handleInputChange('projectAddress', e.target.value)}
                        rows={2}
                      />
                    </div>
                  </div>
                )}

                {/* Step 3: Project Details */}
                {currentStep === 3 && (
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Project Details</h3>
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="scope">Project Scope</Label>
                        <Textarea
                          id="scope"
                          placeholder="Detailed scope of work to be performed..."
                          value={contractData.scope}
                          onChange={(e) => handleInputChange('scope', e.target.value)}
                          rows={3}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="materials">Materials & Specifications</Label>
                        <Textarea
                          id="materials"
                          placeholder="List of materials, brands, specifications..."
                          value={contractData.materials}
                          onChange={(e) => handleInputChange('materials', e.target.value)}
                          rows={3}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="laborDetails">Labor Details</Label>
                        <Textarea
                          id="laborDetails"
                          placeholder="Team size, qualifications, work schedule..."
                          value={contractData.laborDetails}
                          onChange={(e) => handleInputChange('laborDetails', e.target.value)}
                          rows={2}
                        />
                      </div>
                    </div>
                  </div>
                )}

                {/* Step 4: Terms & Conditions */}
                {currentStep === 4 && (
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Terms & Conditions</h3>
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="paymentSchedule">Payment Schedule</Label>
                        <Textarea
                          id="paymentSchedule"
                          placeholder="e.g., 50% upfront, 25% at midpoint, 25% on completion"
                          value={contractData.paymentSchedule}
                          onChange={(e) => handleInputChange('paymentSchedule', e.target.value)}
                          rows={2}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="warranties">Warranties & Guarantees</Label>
                        <Textarea
                          id="warranties"
                          placeholder="Warranty terms, duration, coverage..."
                          value={contractData.warranties}
                          onChange={(e) => handleInputChange('warranties', e.target.value)}
                          rows={2}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="terms">Additional Terms & Conditions</Label>
                        <Textarea
                          id="terms"
                          placeholder="Any additional terms, conditions, or legal clauses..."
                          value={contractData.terms}
                          onChange={(e) => handleInputChange('terms', e.target.value)}
                          rows={3}
                        />
                      </div>
                    </div>
                  </div>
                )}

                {/* Step 5: Customization */}
                {currentStep === 5 && (
                  <div className="space-y-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Customize Your Contract</h3>

                    {/* Color Theme Selection */}
                    <div className="space-y-4">
                      <div className="space-y-3">
                        <Label>Choose Color Theme</Label>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                          {colorThemes.map((theme) => (
                            <button
                              key={theme.name}
                              onClick={() => applyTheme(theme)}
                              className={`p-3 rounded-lg border-2 transition-all hover:scale-105 ${
                                contractData.primaryColor === theme.primary
                                  ? 'border-gray-900 shadow-lg'
                                  : 'border-gray-200 hover:border-gray-300'
                              }`}
                            >
                              <div className="flex items-center gap-2 mb-2">
                                <div
                                  className="w-4 h-4 rounded-full"
                                  style={{ backgroundColor: theme.primary }}
                                />
                                <div
                                  className="w-4 h-4 rounded-full"
                                  style={{ backgroundColor: theme.secondary }}
                                />
                                <div
                                  className="w-4 h-4 rounded-full"
                                  style={{ backgroundColor: theme.accent }}
                                />
                              </div>
                              <p className="text-xs font-medium text-gray-700">{theme.name}</p>
                            </button>
                          ))}
                        </div>
                      </div>

                      {/* Custom Color Pickers */}
                      <div className="space-y-4">
                        <div className="space-y-2">
                          <Label htmlFor="primaryColor">Primary Color</Label>
                          <div className="flex items-center gap-4">
                            <input
                              type="color"
                              id="primaryColor"
                              value={contractData.primaryColor}
                              onChange={(e) => handleInputChange('primaryColor', e.target.value)}
                              className="w-16 h-10 rounded border border-gray-300 cursor-pointer"
                            />
                            <Input
                              value={contractData.primaryColor}
                              onChange={(e) => handleInputChange('primaryColor', e.target.value)}
                              placeholder="#3b82f6"
                              className="flex-1"
                            />
                          </div>
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="backgroundColor">Background Color</Label>
                          <div className="flex items-center gap-4">
                            <input
                              type="color"
                              id="backgroundColor"
                              value={contractData.backgroundColor}
                              onChange={(e) => handleInputChange('backgroundColor', e.target.value)}
                              className="w-16 h-10 rounded border border-gray-300 cursor-pointer"
                            />
                            <Input
                              value={contractData.backgroundColor}
                              onChange={(e) => handleInputChange('backgroundColor', e.target.value)}
                              placeholder="#ffffff"
                              className="flex-1"
                            />
                          </div>
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="textColor">Text Color</Label>
                          <div className="flex items-center gap-4">
                            <input
                              type="color"
                              id="textColor"
                              value={contractData.textColor}
                              onChange={(e) => handleInputChange('textColor', e.target.value)}
                              className="w-16 h-10 rounded border border-gray-300 cursor-pointer"
                            />
                            <Input
                              value={contractData.textColor}
                              onChange={(e) => handleInputChange('textColor', e.target.value)}
                              placeholder="#1f2937"
                              className="flex-1"
                            />
                          </div>
                        </div>
                      </div>

                      {/* Company Name */}
                      <div className="space-y-2">
                        <Label htmlFor="companyName">Company Name</Label>
                        <Input
                          id="companyName"
                          value={contractData.companyName}
                          onChange={(e) => handleInputChange('companyName', e.target.value)}
                          placeholder="Your Company Name"
                        />
                      </div>

                      {/* Logo Upload */}
                      <div className="space-y-2">
                        <Label htmlFor="logo">Company Logo</Label>
                        <div className="flex items-center gap-4">
                          <input
                            type="file"
                            id="logo"
                            accept="image/*"
                            onChange={handleLogoUpload}
                            className="hidden"
                          />
                          <Button
                            type="button"
                            variant="outline"
                            onClick={() => document.getElementById('logo')?.click()}
                            className="flex items-center gap-2"
                          >
                            <Building className="h-4 w-4" />
                            Upload Logo
                          </Button>
                          {contractData.companyLogo && (
                            <div className="flex items-center gap-2">
                              <img
                                src={contractData.companyLogo}
                                alt="Company Logo"
                                className="w-12 h-12 object-contain border rounded"
                              />
                              <span className="text-sm text-green-600">✓ Logo uploaded</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Step 6: Preview */}
                {currentStep === 6 && (
                  <div className="space-y-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Preview Your Contract</h3>

                    <div
                      className="border rounded-lg p-6"
                      style={{
                        borderColor: contractData.primaryColor,
                        backgroundColor: contractData.backgroundColor,
                        color: contractData.textColor
                      }}
                    >
                      {/* Header with logo and company name */}
                      <div className="flex items-center justify-between mb-6 pb-4 border-b" style={{ borderColor: contractData.primaryColor }}>
                        <div className="flex items-center gap-4">
                          {contractData.companyLogo && (
                            <img
                              src={contractData.companyLogo}
                              alt="Company Logo"
                              className="w-16 h-16 object-contain"
                            />
                          )}
                          <div>
                            <h2 className="text-2xl font-bold" style={{ color: contractData.primaryColor }}>
                              {contractData.companyName || "Your Company"}
                            </h2>
                            <p className="text-gray-600">Construction Contract</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-sm text-gray-500">Contract Date</p>
                          <p className="font-semibold">{new Date().toLocaleDateString()}</p>
                        </div>
                      </div>

                      {/* Contract Details Preview */}
                      <div className="space-y-6">
                        <div>
                          <h3 className="font-semibold text-xl mb-3" style={{ color: contractData.primaryColor }}>
                            {contractData.title}
                          </h3>
                          <p className="text-gray-700 leading-relaxed">{contractData.description}</p>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div className="space-y-4">
                            <div>
                              <h4 className="font-semibold mb-3 text-lg" style={{ color: contractData.primaryColor }}>Client Information</h4>
                              <div className="space-y-2 text-sm">
                                <p><strong>Name:</strong> {contractData.clientName}</p>
                                <p><strong>Email:</strong> {contractData.clientEmail}</p>
                                <p><strong>Phone:</strong> {contractData.clientPhone}</p>
                              </div>
                            </div>

                            <div>
                              <h4 className="font-semibold mb-3 text-lg" style={{ color: contractData.primaryColor }}>Project Details</h4>
                              <div className="space-y-2 text-sm">
                                <p><strong>Type:</strong> {contractData.projectType}</p>
                                <p><strong>Address:</strong> {contractData.projectAddress}</p>
                                <p><strong>Total Amount:</strong> <span className="text-lg font-bold" style={{ color: contractData.primaryColor }}>${contractData.totalAmount}</span></p>
                              </div>
                            </div>

                            <div>
                              <h4 className="font-semibold mb-3 text-lg" style={{ color: contractData.primaryColor }}>Timeline</h4>
                              <div className="space-y-2 text-sm">
                                <p><strong>Start Date:</strong> {contractData.startDate}</p>
                                <p><strong>End Date:</strong> {contractData.endDate}</p>
                              </div>
                            </div>
                          </div>

                          <div className="space-y-4">
                            <div>
                              <h4 className="font-semibold mb-3 text-lg" style={{ color: contractData.primaryColor }}>Project Scope</h4>
                              <p className="text-gray-700 text-sm leading-relaxed">{contractData.scope}</p>
                            </div>

                            <div>
                              <h4 className="font-semibold mb-3 text-lg" style={{ color: contractData.primaryColor }}>Payment Schedule</h4>
                              <p className="text-gray-700 text-sm leading-relaxed">{contractData.paymentSchedule}</p>
                            </div>
                          </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div>
                            <h4 className="font-semibold mb-3 text-lg" style={{ color: contractData.primaryColor }}>Materials</h4>
                            <p className="text-gray-700 text-sm leading-relaxed">{contractData.materials}</p>
                          </div>

                          <div>
                            <h4 className="font-semibold mb-3 text-lg" style={{ color: contractData.primaryColor }}>Labor Details</h4>
                            <p className="text-gray-700 text-sm leading-relaxed">{contractData.laborDetails}</p>
                          </div>
                        </div>

                        <div>
                          <h4 className="font-semibold mb-3 text-lg" style={{ color: contractData.primaryColor }}>Terms & Conditions</h4>
                          <p className="text-gray-700 text-sm leading-relaxed">{contractData.terms}</p>
                        </div>

                        <div>
                          <h4 className="font-semibold mb-3 text-lg" style={{ color: contractData.primaryColor }}>Warranties</h4>
                          <p className="text-gray-700 text-sm leading-relaxed">{contractData.warranties}</p>
                        </div>

                        {/* Contract Status */}
                        <div className="mt-6 pt-4 border-t" style={{ borderColor: contractData.primaryColor }}>
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="text-sm text-gray-500">Contract Status</p>
                              <p className="font-semibold" style={{ color: contractData.primaryColor }}>Ready for Signature</p>
                            </div>
                            <div className="text-right">
                              <p className="text-sm text-gray-500">Generated On</p>
                              <p className="font-semibold">{new Date().toLocaleDateString()}</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Navigation Buttons */}
                <div className="flex justify-between pt-6 border-t">
                  <Button
                    variant="outline"
                    onClick={prevStep}
                    disabled={currentStep === 1}
                    className="flex items-center gap-2"
                  >
                    <ArrowLeft className="h-4 w-4" />
                    Previous
                  </Button>

                  {currentStep < 6 ? (
                    <Button onClick={nextStep} className="flex items-center gap-2">
                      {currentStep === 5 ? 'Preview' : 'Next'}
                      <ArrowRight className="h-4 w-4" />
                    </Button>
                  ) : (
                    <Button onClick={handleCreateContract} className="flex items-center gap-2 bg-green-600 hover:bg-green-700">
                      <Send className="h-4 w-4" />
                      Create & Share Contract
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>



          {/* Recent Contracts Tab */}
          <TabsContent value="recent" className="space-y-6">
            <div className="space-y-4">
              {recentContracts.map((contract) => (
                <Card key={contract.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                          <FileText className="h-6 w-6 text-blue-600" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-lg">{contract.title}</h3>
                          <p className="text-gray-600">Client: {contract.clientName}</p>
                          <p className="text-sm text-gray-500">Created: {new Date(contract.createdAt).toLocaleDateString()}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-4">
                        <div className="text-right">
                          <p className="font-semibold text-lg">${parseInt(contract.totalAmount).toLocaleString()}</p>
                          <Badge className={getStatusBadge(contract.status)}>
                            <div className="flex items-center gap-1">
                              {getStatusIcon(contract.status)}
                              {contract.status.charAt(0).toUpperCase() + contract.status.slice(1)}
                            </div>
                          </Badge>
                        </div>
                        <div className="flex gap-2">
                          <Button variant="outline" size="sm">
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button variant="outline" size="sm">
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              if (contract.shareableLink) {
                                navigator.clipboard.writeText(contract.shareableLink).then(() => {
                                  alert(`📋 Contract link copied to clipboard:\n${contract.shareableLink}`);
                                });
                              }
                            }}
                          >
                            <Share className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </ContractDashboardLayout>
  );
};

export default NewContractBuilder;
