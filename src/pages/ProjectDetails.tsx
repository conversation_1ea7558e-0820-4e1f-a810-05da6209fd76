
import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Tabs, Tabs<PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Calendar,
  Users,
  FileText,
  Settings,
  Plus,
  Search,
  Clock,
  Share2,
  Copy,
  ExternalLink,
  Eye,
  CheckCircle,
  Edit,
  Save,
  X,
  Trash2
} from "lucide-react";
import { useParams, Link } from "react-router-dom";
import DashboardLayout from "@/components/DashboardLayout";

const ProjectDetails = () => {
  const { id } = useParams();
  const [clientPortalLink, setClientPortalLink] = useState("");
  const [showClientPortal, setShowClientPortal] = useState(false);
  const [linkCopied, setLinkCopied] = useState(false);

  // Editing states
  const [isEditing, setIsEditing] = useState(false);
  const [editingSection, setEditingSection] = useState<string | null>(null);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Editable project data state
  const [editableProject, setEditableProject] = useState<any>(null);

  // Mock project data - in real app would fetch based on ID
  const project = {
    id: id,
    name: "Downtown Office Complex",
    client: "Sterling Corp",
    contractor: "BuildRight Construction",
    progress: 75,
    budget: 250000,
    spent: 185000,
    status: "active",
    startDate: "Sep 1, 2025",
    dueDate: "Dec 15, 2025",
    description: "A modern 5-story office complex with retail space on ground floor, featuring sustainable design elements and advanced building systems.",
    clientPortalEnabled: true,
    clientPortalToken: `${id}-client-${Math.random().toString(36).substr(2, 9)}`,
    phases: [
      { name: "Foundation", progress: 100, status: "completed", budget: 45000, spent: 43500, dueDate: "Sep 15, 2025" },
      { name: "Structural Framing", progress: 100, status: "completed", budget: 65000, spent: 67200, dueDate: "Oct 10, 2025" },
      { name: "Roofing", progress: 100, status: "completed", budget: 35000, spent: 34800, dueDate: "Nov 1, 2025" },
      { name: "MEP Installation", progress: 60, status: "active", budget: 55000, spent: 25000, dueDate: "Nov 25, 2025" },
      { name: "Interior Finishes", progress: 20, status: "active", budget: 40000, spent: 12000, dueDate: "Dec 10, 2025" },
      { name: "Final Inspection", progress: 0, status: "pending", budget: 10000, spent: 0, dueDate: "Dec 15, 2025" }
    ],
    team: [
      { name: "Mike Johnson", role: "Project Manager", email: "<EMAIL>" },
      { name: "Sarah Chen", role: "Site Supervisor", email: "<EMAIL>" },
      { name: "David Rodriguez", role: "Electrical Contractor", email: "<EMAIL>" },
      { name: "Lisa Park", role: "MEP Engineer", email: "<EMAIL>" }
    ],
    materials: [
      { name: "Steel Reinforcement Bars", quantity: 150, cost: 18750, status: "delivered" },
      { name: "Concrete Mix", quantity: 25, cost: 3750, status: "low-stock" },
      { name: "Electrical Components", quantity: 1, cost: 15000, status: "ordered" }
    ],
    documents: [
      { name: "Building Permits", type: "permit", uploadDate: "Aug 25, 2025", status: "approved" },
      { name: "Architectural Plans v2.1", type: "blueprint", uploadDate: "Nov 10, 2025", status: "current" },
      { name: "MEP Drawings", type: "blueprint", uploadDate: "Nov 5, 2025", status: "current" },
      { name: "Safety Inspection Report", type: "report", uploadDate: "Nov 12, 2025", status: "approved" }
    ]
  };

  const completedPhases = project.phases.filter(p => p.status === "completed").length;
  const remainingBudget = project.budget - project.spent;
  const budgetPercentage = (project.spent / project.budget) * 100;

  // Client Portal Functions
  const generateClientPortalLink = () => {
    const baseUrl = window.location.origin;
    const link = `${baseUrl}/client/${project.clientPortalToken}`;
    setClientPortalLink(link);
    setShowClientPortal(true);
  };

  const copyLinkToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(clientPortalLink);
      setLinkCopied(true);
      setTimeout(() => setLinkCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy link:', err);
    }
  };

  const openClientPortal = () => {
    if (clientPortalLink) {
      window.open(clientPortalLink, '_blank');
    }
  };

  // Editing Functions
  const startEditing = (section: string) => {
    setEditingSection(section);
    setIsEditing(true);
    setEditableProject({ ...project });
  };

  const cancelEditing = () => {
    setEditingSection(null);
    setIsEditing(false);
    setEditableProject(null);
    setHasUnsavedChanges(false);
  };

  const saveChanges = () => {
    // In a real app, this would make an API call to save the changes
    console.log('Saving project changes:', editableProject);

    // Simulate API call
    setTimeout(() => {
      // Update the project data (in real app, this would be handled by state management)
      Object.assign(project, editableProject);
      setEditingSection(null);
      setIsEditing(false);
      setEditableProject(null);
      setHasUnsavedChanges(false);

      // Show success message (you could add a toast notification here)
      alert('✅ Project updated successfully!\n\n🔄 Changes are now live on the client portal.\n📱 Clients will see the updated information immediately.');
    }, 500);
  };

  const updateEditableProject = (field: string, value: any) => {
    setEditableProject((prev: any) => ({
      ...prev,
      [field]: value
    }));
    setHasUnsavedChanges(true);
  };

  const updatePhase = (phaseIndex: number, field: string, value: any) => {
    setEditableProject((prev: any) => ({
      ...prev,
      phases: prev.phases.map((phase: any, index: number) =>
        index === phaseIndex ? { ...phase, [field]: value } : phase
      )
    }));
    setHasUnsavedChanges(true);
  };

  const updateMaterial = (materialIndex: number, field: string, value: any) => {
    setEditableProject((prev: any) => ({
      ...prev,
      materials: prev.materials.map((material: any, index: number) =>
        index === materialIndex ? { ...material, [field]: value } : material
      )
    }));
    setHasUnsavedChanges(true);
  };

  const addNewPhase = () => {
    const newPhase = {
      name: "New Phase",
      progress: 0,
      status: "pending",
      budget: 0,
      spent: 0,
      dueDate: new Date().toLocaleDateString()
    };
    setEditableProject((prev: any) => ({
      ...prev,
      phases: [...prev.phases, newPhase]
    }));
    setHasUnsavedChanges(true);
  };

  const removePhase = (phaseIndex: number) => {
    setEditableProject((prev: any) => ({
      ...prev,
      phases: prev.phases.filter((_: any, index: number) => index !== phaseIndex)
    }));
    setHasUnsavedChanges(true);
  };

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Contractor Management Notice */}
        <Alert className="mb-6 border-blue-500/20 bg-blue-50/50">
          <Edit className="h-4 w-4" />
          <AlertDescription>
            <strong>Contractor Management Interface:</strong> Edit and update project details here. All changes will automatically reflect on the client portal in real-time.
          </AlertDescription>
        </Alert>

        {/* Project Header */}
        <div className="mb-8">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
            <div>
              <div className="flex items-center space-x-2 mb-2">
                <Link to="/dashboard" className="text-muted-foreground hover:text-primary">
                  Dashboard
                </Link>
                <span className="text-muted-foreground">/</span>
                <span>Project Details</span>
              </div>
              <h1 className="text-3xl font-bold mb-2">{project.name}</h1>
              <div className="flex flex-wrap items-center gap-4 text-muted-foreground">
                <span>Client: {project.client}</span>
                <span>•</span>
                <span>Contractor: {project.contractor}</span>
                <span>•</span>
                <span>Started: {project.startDate}</span>
                <span>•</span>
                <span>Due: {project.dueDate}</span>
              </div>
            </div>
            <div className="flex items-center space-x-2 mt-4 md:mt-0">
              {hasUnsavedChanges && (
                <div className="flex items-center space-x-2">
                  <Button
                    variant="default"
                    onClick={saveChanges}
                    className="flex items-center"
                  >
                    <Save className="mr-2 h-4 w-4" />
                    Save Changes
                  </Button>
                  <Button
                    variant="outline"
                    onClick={cancelEditing}
                    className="flex items-center"
                  >
                    <X className="mr-2 h-4 w-4" />
                    Cancel
                  </Button>
                </div>
              )}
              {!isEditing && (
                <Button
                  variant="outline"
                  onClick={() => startEditing('overview')}
                  className="flex items-center"
                >
                  <Edit className="mr-2 h-4 w-4" />
                  Edit Project
                </Button>
              )}
              <Button
                variant="outline"
                onClick={generateClientPortalLink}
                className="flex items-center"
              >
                <Share2 className="mr-2 h-4 w-4" />
                Generate Client Link
              </Button>
            </div>
          </div>
          
          {editingSection === 'overview' ? (
            <div className="max-w-3xl">
              <Label htmlFor="project-description">Project Description</Label>
              <Textarea
                id="project-description"
                value={editableProject?.description || ''}
                onChange={(e) => updateEditableProject('description', e.target.value)}
                className="mt-1"
                rows={3}
              />
            </div>
          ) : (
            <p className="text-muted-foreground max-w-3xl">{project.description}</p>
          )}
        </div>

        {/* Client Portal Link Section */}
        {showClientPortal && (
          <Card className="border-2 border-blue-500/20 bg-blue-50/50">
            <CardHeader>
              <CardTitle className="flex items-center text-blue-700">
                <Share2 className="mr-2 h-5 w-5" />
                Client Portal Link Generated
              </CardTitle>
              <CardDescription>
                Share this secure link with your client to give them real-time access to project updates,
                progress tracking, and the ability to leave comments and feedback.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="clientLink">Shareable Client Portal Link</Label>
                <div className="flex space-x-2">
                  <Input
                    id="clientLink"
                    value={clientPortalLink}
                    readOnly
                    className="font-mono text-sm"
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={copyLinkToClipboard}
                    className="flex items-center"
                  >
                    {linkCopied ? (
                      <>
                        <CheckCircle className="mr-2 h-4 w-4 text-green-600" />
                        Copied!
                      </>
                    ) : (
                      <>
                        <Copy className="mr-2 h-4 w-4" />
                        Copy
                      </>
                    )}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={openClientPortal}
                    className="flex items-center"
                  >
                    <ExternalLink className="mr-2 h-4 w-4" />
                    Preview
                  </Button>
                </div>
              </div>

              <Alert>
                <Eye className="h-4 w-4" />
                <AlertDescription>
                  <strong>Client Access Features:</strong>
                  <ul className="mt-2 space-y-1 text-sm">
                    <li>• Real-time project progress and milestone tracking</li>
                    <li>• Budget and timeline visibility</li>
                    <li>• Document and photo gallery access</li>
                    <li>• Comment and feedback system</li>
                    <li>• Activity feed with latest updates</li>
                  </ul>
                </AlertDescription>
              </Alert>

              <div className="flex items-center justify-between pt-2 border-t">
                <div className="text-sm text-muted-foreground">
                  <span className="font-medium">Access Token:</span> {project.clientPortalToken}
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowClientPortal(false)}
                >
                  Dismiss
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="border-2 border-primary/20">
            <CardHeader className="text-center pb-2">
              <CardTitle className="text-lg">Progress</CardTitle>
              <div className="text-3xl font-bold text-primary">{project.progress}%</div>
            </CardHeader>
            <CardContent>
              <Progress value={project.progress} className="mb-2" />
              <p className="text-xs text-center text-muted-foreground">
                {completedPhases} of {project.phases.length} phases complete
              </p>
            </CardContent>
          </Card>

          <Card className="border-2 border-green-500/20">
            <CardHeader className="text-center pb-2">
              <CardTitle className="text-lg">Budget</CardTitle>
              <div className="text-3xl font-bold text-green-600">
                ${(project.budget / 1000).toFixed(0)}K
              </div>
            </CardHeader>
            <CardContent>
              <Progress value={budgetPercentage} className="mb-2" />
              <p className="text-xs text-center text-muted-foreground">
                ${(remainingBudget / 1000).toFixed(0)}K remaining
              </p>
            </CardContent>
          </Card>

          <Card className="border-2 border-blue-500/20">
            <CardHeader className="text-center pb-2">
              <CardTitle className="text-lg">Timeline</CardTitle>
              <div className="text-3xl font-bold text-blue-600">45</div>
            </CardHeader>
            <CardContent>
              <p className="text-xs text-center text-muted-foreground mb-2">Days remaining</p>
              <Badge variant="default" className="w-full justify-center bg-green-500">
                On Schedule
              </Badge>
            </CardContent>
          </Card>

          <Card className="border-2 border-orange-500/20">
            <CardHeader className="text-center pb-2">
              <CardTitle className="text-lg">Team</CardTitle>
              <div className="text-3xl font-bold text-orange-600">{project.team.length}</div>
            </CardHeader>
            <CardContent>
              <p className="text-xs text-center text-muted-foreground">
                Active team members
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Project Details Tabs */}
        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="grid w-full grid-cols-7">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="costs">Cost Tracking</TabsTrigger>
            <TabsTrigger value="materials">Materials</TabsTrigger>
            <TabsTrigger value="labor">Labor</TabsTrigger>
            <TabsTrigger value="progress">Progress</TabsTrigger>
            <TabsTrigger value="documents">Documents</TabsTrigger>
            <TabsTrigger value="client">Client Portal</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle>Project Phases</CardTitle>
                    <CardDescription>
                      Track progress and manage each phase of the construction project
                    </CardDescription>
                  </div>
                  <div className="flex space-x-2">
                    {editingSection === 'overview' ? (
                      <div className="flex space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={addNewPhase}
                          className="flex items-center"
                        >
                          <Plus className="mr-2 h-4 w-4" />
                          Add Phase
                        </Button>
                        <Button
                          variant="default"
                          size="sm"
                          onClick={saveChanges}
                          className="flex items-center"
                        >
                          <Save className="mr-2 h-4 w-4" />
                          Save
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={cancelEditing}
                          className="flex items-center"
                        >
                          <X className="mr-2 h-4 w-4" />
                          Cancel
                        </Button>
                      </div>
                    ) : (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => startEditing('overview')}
                        className="flex items-center"
                      >
                        <Edit className="mr-2 h-4 w-4" />
                        Edit Phases
                      </Button>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {(editingSection === 'overview' ? editableProject?.phases : project.phases)?.map((phase: any, index: number) => (
                    <div key={index} className="border rounded-lg p-4">
                      {editingSection === 'overview' ? (
                        // Editable Phase
                        <div className="space-y-4">
                          <div className="flex justify-between items-start">
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 flex-1">
                              <div>
                                <Label htmlFor={`phase-name-${index}`}>Phase Name</Label>
                                <Input
                                  id={`phase-name-${index}`}
                                  value={phase.name}
                                  onChange={(e) => updatePhase(index, 'name', e.target.value)}
                                  className="mt-1"
                                />
                              </div>
                              <div>
                                <Label htmlFor={`phase-status-${index}`}>Status</Label>
                                <Select
                                  value={phase.status}
                                  onValueChange={(value) => updatePhase(index, 'status', value)}
                                >
                                  <SelectTrigger className="mt-1">
                                    <SelectValue />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="pending">Pending</SelectItem>
                                    <SelectItem value="active">Active</SelectItem>
                                    <SelectItem value="completed">Completed</SelectItem>
                                  </SelectContent>
                                </Select>
                              </div>
                              <div>
                                <Label htmlFor={`phase-due-${index}`}>Due Date</Label>
                                <Input
                                  id={`phase-due-${index}`}
                                  value={phase.dueDate}
                                  onChange={(e) => updatePhase(index, 'dueDate', e.target.value)}
                                  className="mt-1"
                                />
                              </div>
                            </div>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => removePhase(index)}
                              className="ml-2 text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                              <Label htmlFor={`phase-progress-${index}`}>Progress (%)</Label>
                              <Input
                                id={`phase-progress-${index}`}
                                type="number"
                                min="0"
                                max="100"
                                value={phase.progress}
                                onChange={(e) => updatePhase(index, 'progress', parseInt(e.target.value) || 0)}
                                className="mt-1"
                              />
                            </div>
                            <div>
                              <Label htmlFor={`phase-budget-${index}`}>Budget ($)</Label>
                              <Input
                                id={`phase-budget-${index}`}
                                type="number"
                                value={phase.budget}
                                onChange={(e) => updatePhase(index, 'budget', parseInt(e.target.value) || 0)}
                                className="mt-1"
                              />
                            </div>
                            <div>
                              <Label htmlFor={`phase-spent-${index}`}>Spent ($)</Label>
                              <Input
                                id={`phase-spent-${index}`}
                                type="number"
                                value={phase.spent}
                                onChange={(e) => updatePhase(index, 'spent', parseInt(e.target.value) || 0)}
                                className="mt-1"
                              />
                            </div>
                          </div>
                        </div>
                      ) : (
                        // Read-only Phase
                        <div>
                          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-4">
                            <div className="flex items-center space-x-3">
                              <div className={`w-4 h-4 rounded-full ${
                                phase.status === "completed" ? "bg-green-500" :
                                phase.status === "active" ? "bg-blue-500" : "bg-gray-300"
                              }`}></div>
                              <div>
                                <h3 className="font-semibold text-lg">{phase.name}</h3>
                                <p className="text-sm text-muted-foreground">Due: {phase.dueDate}</p>
                              </div>
                            </div>
                            <div className="flex items-center space-x-2 mt-2 md:mt-0">
                              <Badge variant={phase.status === "completed" ? "default" : "outline"}>
                                {phase.status}
                              </Badge>
                              <span className="text-sm font-medium">{phase.progress}%</span>
                            </div>
                          </div>

                          <div className="grid md:grid-cols-2 gap-6">
                            <div>
                              <Progress value={phase.progress} className="mb-2" />
                              <div className="flex justify-between text-sm text-muted-foreground">
                                <span>Progress</span>
                                <span>{phase.progress}% Complete</span>
                              </div>
                            </div>
                            <div>
                              <div className="flex justify-between text-sm mb-1">
                                <span>Budget</span>
                                <span className="font-medium">${(phase.budget / 1000).toFixed(0)}K</span>
                              </div>
                              <div className="flex justify-between text-sm mb-1">
                                <span>Spent</span>
                                <span className={`font-medium ${
                                  phase.spent > phase.budget ? "text-red-600" : "text-green-600"
                                }`}>
                                  ${(phase.spent / 1000).toFixed(0)}K
                                </span>
                              </div>
                              <Progress
                                value={Math.min((phase.spent / phase.budget) * 100, 100)}
                                className="h-2"
                              />
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="costs" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Total Budget</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">${(project.budget / 1000).toFixed(0)}K</div>
                  <p className="text-xs text-muted-foreground">Project allocation</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Total Spent</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-orange-600">${(project.spent / 1000).toFixed(0)}K</div>
                  <p className="text-xs text-muted-foreground">{budgetPercentage.toFixed(1)}% of budget</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Remaining</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-green-600">${(remainingBudget / 1000).toFixed(0)}K</div>
                  <p className="text-xs text-muted-foreground">{(100 - budgetPercentage).toFixed(1)}% remaining</p>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle>Cost Breakdown by Phase</CardTitle>
                    <CardDescription>Detailed cost analysis for each project phase</CardDescription>
                  </div>
                  <div className="flex space-x-2">
                    {editingSection === 'costs' ? (
                      <div className="flex space-x-2">
                        <Button
                          variant="default"
                          size="sm"
                          onClick={saveChanges}
                          className="flex items-center"
                        >
                          <Save className="mr-2 h-4 w-4" />
                          Save
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={cancelEditing}
                          className="flex items-center"
                        >
                          <X className="mr-2 h-4 w-4" />
                          Cancel
                        </Button>
                      </div>
                    ) : (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => startEditing('costs')}
                        className="flex items-center"
                      >
                        <Edit className="mr-2 h-4 w-4" />
                        Edit Costs
                      </Button>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {project.phases.map((phase, index) => (
                    <div key={index} className="border rounded-lg p-4">
                      <div className="flex justify-between items-start mb-3">
                        <div>
                          <h3 className="font-medium">{phase.name}</h3>
                          <Badge variant={
                            phase.status === "completed" ? "default" :
                            phase.status === "active" ? "secondary" : "outline"
                          }>
                            {phase.status}
                          </Badge>
                        </div>
                        <div className="text-right">
                          <div className="text-sm text-muted-foreground">Budget vs Spent</div>
                          <div className="font-medium">
                            ${(phase.spent / 1000).toFixed(0)}K / ${(phase.budget / 1000).toFixed(0)}K
                          </div>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>Budget Utilization</span>
                          <span className={`font-medium ${
                            phase.spent > phase.budget ? "text-red-600" : "text-green-600"
                          }`}>
                            {((phase.spent / phase.budget) * 100).toFixed(1)}%
                          </span>
                        </div>
                        <Progress
                          value={Math.min((phase.spent / phase.budget) * 100, 100)}
                          className="h-2"
                        />
                        {phase.spent > phase.budget && (
                          <div className="text-xs text-red-600">
                            Over budget by ${((phase.spent - phase.budget) / 1000).toFixed(1)}K
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="materials">
            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle>Material Inventory</CardTitle>
                    <CardDescription>
                      Materials allocated to this project
                    </CardDescription>
                  </div>
                  <div className="flex space-x-2">
                    {editingSection === 'materials' ? (
                      <div className="flex space-x-2">
                        <Button
                          variant="default"
                          size="sm"
                          onClick={saveChanges}
                          className="flex items-center"
                        >
                          <Save className="mr-2 h-4 w-4" />
                          Save
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={cancelEditing}
                          className="flex items-center"
                        >
                          <X className="mr-2 h-4 w-4" />
                          Cancel
                        </Button>
                      </div>
                    ) : (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => startEditing('materials')}
                        className="flex items-center"
                      >
                        <Edit className="mr-2 h-4 w-4" />
                        Edit Materials
                      </Button>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {(editingSection === 'materials' ? editableProject?.materials : project.materials)?.map((material: any, index: number) => (
                    <div key={index} className="p-4 border rounded-lg">
                      {editingSection === 'materials' ? (
                        // Editable Material
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                          <div>
                            <Label htmlFor={`material-name-${index}`}>Material Name</Label>
                            <Input
                              id={`material-name-${index}`}
                              value={material.name}
                              onChange={(e) => updateMaterial(index, 'name', e.target.value)}
                              className="mt-1"
                            />
                          </div>
                          <div>
                            <Label htmlFor={`material-quantity-${index}`}>Quantity</Label>
                            <Input
                              id={`material-quantity-${index}`}
                              type="number"
                              value={material.quantity}
                              onChange={(e) => updateMaterial(index, 'quantity', parseInt(e.target.value) || 0)}
                              className="mt-1"
                            />
                          </div>
                          <div>
                            <Label htmlFor={`material-cost-${index}`}>Cost ($)</Label>
                            <Input
                              id={`material-cost-${index}`}
                              type="number"
                              value={material.cost}
                              onChange={(e) => updateMaterial(index, 'cost', parseInt(e.target.value) || 0)}
                              className="mt-1"
                            />
                          </div>
                          <div>
                            <Label htmlFor={`material-status-${index}`}>Status</Label>
                            <Select
                              value={material.status}
                              onValueChange={(value) => updateMaterial(index, 'status', value)}
                            >
                              <SelectTrigger className="mt-1">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="ordered">Ordered</SelectItem>
                                <SelectItem value="delivered">Delivered</SelectItem>
                                <SelectItem value="low-stock">Low Stock</SelectItem>
                                <SelectItem value="out-of-stock">Out of Stock</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                      ) : (
                        // Read-only Material
                        <div className="flex justify-between items-center">
                          <div>
                            <h3 className="font-medium">{material.name}</h3>
                            <p className="text-sm text-muted-foreground">
                              Quantity: {material.quantity} • Cost: ${material.cost.toLocaleString()}
                            </p>
                          </div>
                          <Badge variant={
                            material.status === "delivered" ? "default" :
                            material.status === "low-stock" ? "destructive" : "secondary"
                          }>
                            {material.status.replace('-', ' ')}
                          </Badge>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="labor" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Total Team</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{project.team.length}</div>
                  <p className="text-xs text-muted-foreground">Active members</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Labor Cost</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">$85K</div>
                  <p className="text-xs text-muted-foreground">34% of budget</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Hours Logged</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">1,240</div>
                  <p className="text-xs text-muted-foreground">This month</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Efficiency</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-green-600">94%</div>
                  <p className="text-xs text-muted-foreground">Above target</p>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle>Team Members & Roles</CardTitle>
                    <CardDescription>Labor management and workforce allocation</CardDescription>
                  </div>
                  <div className="flex space-x-2">
                    {editingSection === 'labor' ? (
                      <div className="flex space-x-2">
                        <Button
                          variant="default"
                          size="sm"
                          onClick={saveChanges}
                          className="flex items-center"
                        >
                          <Save className="mr-2 h-4 w-4" />
                          Save
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={cancelEditing}
                          className="flex items-center"
                        >
                          <X className="mr-2 h-4 w-4" />
                          Cancel
                        </Button>
                      </div>
                    ) : (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => startEditing('labor')}
                        className="flex items-center"
                      >
                        <Edit className="mr-2 h-4 w-4" />
                        Edit Team
                      </Button>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {project.team.map((member, index) => (
                    <div key={index} className="border rounded-lg p-4">
                      <div className="flex justify-between items-start mb-3">
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                            <Users className="h-5 w-5 text-primary" />
                          </div>
                          <div>
                            <h3 className="font-medium">{member.name}</h3>
                            <p className="text-sm text-muted-foreground">{member.role}</p>
                            <p className="text-xs text-muted-foreground">{member.email}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <Badge variant="outline">Active</Badge>
                          <div className="text-xs text-muted-foreground mt-1">
                            40 hrs/week
                          </div>
                        </div>
                      </div>
                      <div className="grid grid-cols-3 gap-4 text-sm">
                        <div>
                          <span className="text-muted-foreground">Hours This Week:</span>
                          <div className="font-medium">38 hrs</div>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Rate:</span>
                          <div className="font-medium">$45/hr</div>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Total Cost:</span>
                          <div className="font-medium">$1,710</div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="progress" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Overall Progress</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{project.progress}%</div>
                  <Progress value={project.progress} className="mt-2" />
                  <p className="text-xs text-muted-foreground mt-1">On schedule</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Completed Phases</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{completedPhases}</div>
                  <p className="text-xs text-muted-foreground">of {project.phases.length} phases</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Days Remaining</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-blue-600">45</div>
                  <p className="text-xs text-muted-foreground">Until completion</p>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle>Progress Timeline</CardTitle>
                    <CardDescription>Detailed progress tracking by phase</CardDescription>
                  </div>
                  <div className="flex space-x-2">
                    {editingSection === 'progress' ? (
                      <div className="flex space-x-2">
                        <Button
                          variant="default"
                          size="sm"
                          onClick={saveChanges}
                          className="flex items-center"
                        >
                          <Save className="mr-2 h-4 w-4" />
                          Save
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={cancelEditing}
                          className="flex items-center"
                        >
                          <X className="mr-2 h-4 w-4" />
                          Cancel
                        </Button>
                      </div>
                    ) : (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => startEditing('progress')}
                        className="flex items-center"
                      >
                        <Edit className="mr-2 h-4 w-4" />
                        Edit Progress
                      </Button>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {project.phases.map((phase, index) => (
                    <div key={index} className="relative">
                      <div className="flex items-center space-x-4">
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                          phase.status === "completed" ? "bg-green-500 text-white" :
                          phase.status === "active" ? "bg-blue-500 text-white" :
                          "bg-gray-200 text-gray-500"
                        }`}>
                          {phase.status === "completed" ? (
                            <CheckCircle className="h-4 w-4" />
                          ) : (
                            <Clock className="h-4 w-4" />
                          )}
                        </div>
                        <div className="flex-1">
                          <div className="flex justify-between items-center mb-2">
                            <h3 className="font-medium">{phase.name}</h3>
                            <div className="flex items-center space-x-2">
                              <Badge variant={
                                phase.status === "completed" ? "default" :
                                phase.status === "active" ? "secondary" : "outline"
                              }>
                                {phase.status}
                              </Badge>
                              <span className="text-sm text-muted-foreground">
                                Due: {phase.dueDate}
                              </span>
                            </div>
                          </div>
                          <div className="space-y-2">
                            <div className="flex justify-between text-sm">
                              <span>Progress</span>
                              <span className="font-medium">{phase.progress}%</span>
                            </div>
                            <Progress value={phase.progress} className="h-2" />
                          </div>
                        </div>
                      </div>
                      {index < project.phases.length - 1 && (
                        <div className="w-px h-6 bg-gray-200 ml-4 mt-2"></div>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="team">
            <Card>
              <CardHeader>
                <CardTitle>Project Team</CardTitle>
                <CardDescription>
                  Team members assigned to this project
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-2 gap-4">
                  {project.team.map((member, index) => (
                    <div key={index} className="flex items-center space-x-3 p-4 border rounded-lg">
                      <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                        <Users className="h-5 w-5 text-primary" />
                      </div>
                      <div>
                        <h3 className="font-medium">{member.name}</h3>
                        <p className="text-sm text-muted-foreground">{member.role}</p>
                        <p className="text-xs text-muted-foreground">{member.email}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="documents">
            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle>Project Documents</CardTitle>
                    <CardDescription>
                      Blueprints, permits, reports, and other project documentation
                    </CardDescription>
                  </div>
                  <div className="flex space-x-2">
                    {editingSection === 'documents' ? (
                      <div className="flex space-x-2">
                        <Button
                          variant="default"
                          size="sm"
                          onClick={saveChanges}
                          className="flex items-center"
                        >
                          <Save className="mr-2 h-4 w-4" />
                          Save
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={cancelEditing}
                          className="flex items-center"
                        >
                          <X className="mr-2 h-4 w-4" />
                          Cancel
                        </Button>
                      </div>
                    ) : (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => startEditing('documents')}
                        className="flex items-center"
                      >
                        <Edit className="mr-2 h-4 w-4" />
                        Manage Documents
                      </Button>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {project.documents.map((doc, index) => (
                    <div key={index} className="flex justify-between items-center p-4 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        <FileText className="h-5 w-5 text-muted-foreground" />
                        <div>
                          <h3 className="font-medium">{doc.name}</h3>
                          <p className="text-sm text-muted-foreground">
                            {doc.type} • Uploaded {doc.uploadDate}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant={doc.status === "approved" ? "default" : "secondary"}>
                          {doc.status}
                        </Badge>
                        <Button variant="outline" size="sm">
                          View
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="client" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Portal Status</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center space-x-2">
                    <div className={`w-3 h-3 rounded-full ${project.clientPortalEnabled ? 'bg-green-500' : 'bg-gray-400'}`}></div>
                    <span className="font-medium">{project.clientPortalEnabled ? 'Active' : 'Inactive'}</span>
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">Client access enabled</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Client Comments</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">3</div>
                  <p className="text-xs text-muted-foreground">Pending responses</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Last Client Visit</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">2h</div>
                  <p className="text-xs text-muted-foreground">ago</p>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Client Portal Management</CardTitle>
                <CardDescription>Manage client access and communication</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <h3 className="font-medium">Portal Access</h3>
                    <p className="text-sm text-muted-foreground">
                      Allow {project.client} to view project updates and leave comments
                    </p>
                  </div>
                  <Badge variant={project.clientPortalEnabled ? "default" : "secondary"}>
                    {project.clientPortalEnabled ? "Enabled" : "Disabled"}
                  </Badge>
                </div>

                {project.clientPortalEnabled && (
                  <div className="space-y-4">
                    <div className="p-4 border rounded-lg bg-blue-50/50">
                      <h4 className="font-medium mb-2">Client Portal Link</h4>
                      <div className="flex items-center space-x-2">
                        <Input
                          value={clientPortalLink || `${window.location.origin}/client/${project.clientPortalToken}`}
                          readOnly
                          className="flex-1"
                        />
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            const link = `${window.location.origin}/client/${project.clientPortalToken}`;
                            navigator.clipboard.writeText(link);
                            setLinkCopied(true);
                            setTimeout(() => setLinkCopied(false), 2000);
                          }}
                        >
                          {linkCopied ? <CheckCircle className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => window.open(`${window.location.origin}/client/${project.clientPortalToken}`, '_blank')}
                        >
                          <ExternalLink className="h-4 w-4" />
                        </Button>
                      </div>
                      <p className="text-xs text-muted-foreground mt-2">
                        Share this link with {project.client} to give them access to the project portal
                      </p>
                    </div>

                    <div className="space-y-3">
                      <h4 className="font-medium">Recent Client Activity</h4>
                      <div className="space-y-2">
                        <div className="flex justify-between items-center p-3 border rounded">
                          <div>
                            <p className="text-sm font-medium">New comment on MEP Installation</p>
                            <p className="text-xs text-muted-foreground">2 hours ago</p>
                          </div>
                          <Badge variant="outline">Unread</Badge>
                        </div>
                        <div className="flex justify-between items-center p-3 border rounded">
                          <div>
                            <p className="text-sm font-medium">Viewed progress update</p>
                            <p className="text-xs text-muted-foreground">1 day ago</p>
                          </div>
                          <Badge variant="secondary">Read</Badge>
                        </div>
                        <div className="flex justify-between items-center p-3 border rounded">
                          <div>
                            <p className="text-sm font-medium">Downloaded latest photos</p>
                            <p className="text-xs text-muted-foreground">3 days ago</p>
                          </div>
                          <Badge variant="secondary">Read</Badge>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
};

export default ProjectDetails;
