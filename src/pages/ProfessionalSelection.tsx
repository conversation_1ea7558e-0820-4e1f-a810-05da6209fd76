import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Settings, 
  FileText, 
  ArrowRight, 
  CheckCircle, 
  Users, 
  Calendar, 
  DollarSign,
  Shield,
  Zap,
  Building,
  PenTool,
  Briefcase
} from "lucide-react";
import { Link } from "react-router-dom";

const ProfessionalSelection = () => {
  const platformFeatures = [
    "Complete Project Management",
    "Cost & Budget Tracking", 
    "Material & Labor Management",
    "Client Portal & Communication",
    "Task Scheduling & Progress Tracking",
    "Document Management",
    "Real-time Collaboration"
  ];

  const contractFeatures = [
    "Professional Contract Templates",
    "Quotation Builder & Calculator", 
    "E-signature Integration",
    "Client Review & Approval",
    "Real-time Document Sharing",
    "Version Control & Audit Trail",
    "PDF Generation & Export"
  ];

  const professionalTypes = [
    {
      icon: <Building className="h-6 w-6" />,
      title: "General Contractors",
      description: "Manage complete construction projects"
    },
    {
      icon: <PenTool className="h-6 w-6" />,
      title: "Architects & Designers", 
      description: "Create contracts for design services"
    },
    {
      icon: <Briefcase className="h-6 w-6" />,
      title: "Specialized Contractors",
      description: "Electrical, plumbing, HVAC specialists"
    },
    {
      icon: <Users className="h-6 w-6" />,
      title: "Project Developers",
      description: "Real estate and development projects"
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b bg-white/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <Link to="/" className="flex items-center space-x-2">
              <div className="h-8 w-8 construction-gradient rounded-lg flex items-center justify-center">
                <Settings className="h-5 w-5 text-white" />
              </div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
                ConstructionSync
              </h1>
            </Link>
            <div className="flex items-center space-x-4">
              <Link to="/login" className="text-muted-foreground hover:text-primary transition-colors">
                Sign In
              </Link>
              <Button variant="outline" asChild>
                <Link to="/">Back to Home</Link>
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-16 bg-gradient-to-br from-blue-50 via-white to-orange-50">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-4xl mx-auto mb-12">
            <Badge className="mb-4 bg-primary/10 text-primary border-primary/20">
              Choose Your Professional Solution
            </Badge>
            <h1 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-primary via-blue-600 to-accent bg-clip-text text-transparent">
              Select Your Platform
            </h1>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Choose the solution that best fits your professional needs. Whether you need complete project management or specialized contract tools.
            </p>
          </div>

          {/* Professional Types */}
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4 mb-12">
            {professionalTypes.map((type, index) => (
              <Card key={index} className="text-center hover:shadow-md transition-shadow">
                <CardHeader className="pb-3">
                  <div className="mx-auto mb-2 p-2 bg-primary/10 rounded-lg text-primary w-fit">
                    {type.icon}
                  </div>
                  <CardTitle className="text-sm font-medium">{type.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-xs">{type.description}</CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Platform Options */}
          <div className="grid lg:grid-cols-2 gap-8 max-w-6xl mx-auto">
            {/* Full Platform */}
            <Card className="relative border-2 border-primary/20 hover:border-primary/40 transition-all duration-300 hover:shadow-xl">
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <Badge className="bg-primary text-white">Most Popular</Badge>
              </div>
              <CardHeader className="text-center pt-8">
                <div className="mx-auto mb-4 p-4 bg-primary/10 rounded-xl text-primary">
                  <Settings className="h-12 w-12" />
                </div>
                <CardTitle className="text-2xl mb-2">ConstructionSync Platform</CardTitle>
                <CardDescription className="text-base">
                  Complete construction project management solution
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-3">
                  {platformFeatures.map((feature, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                      <span className="text-sm">{feature}</span>
                    </div>
                  ))}
                </div>
                
                <div className="pt-4 space-y-3">
                  <Button asChild className="w-full" size="lg">
                    <Link to="/register">
                      Get Started Free
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Link>
                  </Button>
                  <Button variant="outline" asChild className="w-full">
                    <Link to="/login">
                      Sign In to Platform
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Contract & Quotation Tools */}
            <Card className="border-2 border-accent/20 hover:border-accent/40 transition-all duration-300 hover:shadow-xl">
              <CardHeader className="text-center">
                <div className="mx-auto mb-4 p-4 bg-accent/10 rounded-xl text-accent">
                  <FileText className="h-12 w-12" />
                </div>
                <CardTitle className="text-2xl mb-2">Contract & Quotation Tools</CardTitle>
                <CardDescription className="text-base">
                  Specialized tools for creating professional contracts and quotations
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-3">
                  {contractFeatures.map((feature, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                      <span className="text-sm">{feature}</span>
                    </div>
                  ))}
                </div>

                <div className="pt-4 space-y-3">
                  <div className="grid grid-cols-2 gap-2">
                    <Button asChild className="bg-primary hover:bg-primary/90" size="lg">
                      <Link to="/contracts">
                        Contract Builder
                      </Link>
                    </Button>
                    <Button asChild className="bg-accent hover:bg-accent/90" size="lg">
                      <Link to="/quotations">
                        Quotation Builder
                      </Link>
                    </Button>
                  </div>
                  <Button variant="outline" asChild className="w-full border-accent text-accent hover:bg-accent hover:text-white">
                    <Link to="/register">
                      Create Account for Full Access
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-16 bg-muted/30">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Why Choose ConstructionSync?</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Built specifically for construction professionals with enterprise-grade features
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            <div className="text-center">
              <div className="mx-auto mb-4 p-3 bg-primary/10 rounded-lg text-primary w-fit">
                <Shield className="h-6 w-6" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Secure & Reliable</h3>
              <p className="text-muted-foreground">Enterprise-grade security with 99.9% uptime guarantee</p>
            </div>
            <div className="text-center">
              <div className="mx-auto mb-4 p-3 bg-primary/10 rounded-lg text-primary w-fit">
                <Zap className="h-6 w-6" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Easy to Use</h3>
              <p className="text-muted-foreground">Intuitive interface designed for construction workflows</p>
            </div>
            <div className="text-center">
              <div className="mx-auto mb-4 p-3 bg-primary/10 rounded-lg text-primary w-fit">
                <Users className="h-6 w-6" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Team Collaboration</h3>
              <p className="text-muted-foreground">Seamless collaboration between all project stakeholders</p>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-8 border-t bg-muted/30">
        <div className="container mx-auto px-4 text-center">
          <div className="flex items-center justify-center space-x-2 mb-4">
            <div className="h-6 w-6 construction-gradient rounded flex items-center justify-center">
              <Settings className="h-4 w-4 text-white" />
            </div>
            <span className="font-bold">ConstructionSync</span>
          </div>
          <p className="text-sm text-muted-foreground">
            &copy; 2025 ConstructionSync. All rights reserved.
          </p>
        </div>
      </footer>
    </div>
  );
};

export default ProfessionalSelection;
