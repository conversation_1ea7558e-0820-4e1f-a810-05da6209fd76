import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Input } from "@/components/ui/input";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Settings, Users, Calendar, Plus, FileText, Search, Share2, MessageCircle, Filter } from "lucide-react";
import { Link } from "react-router-dom";
import { useState } from "react";
import DashboardLayout from "@/components/DashboardLayout";

const Projects = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");

  const projects = [
    {
      id: "1",
      name: "Downtown Office Complex",
      client: "Sterling Corp",
      progress: 75,
      budget: 250000,
      spent: 185000,
      status: "active",
      dueDate: "Dec 15, 2025",
      lastUpdate: "2 hours ago",
      clientPortalEnabled: true,
      clientPortalToken: "1-client-abc123",
      unreadComments: 2,
      description: "Modern 12-story office building with sustainable design features",
      startDate: "Jan 15, 2025",
      location: "Downtown District",
      contractor: "BuildCorp Solutions"
    },
    {
      id: "2", 
      name: "Residential Villa",
      client: "Johnson Family",
      progress: 45,
      budget: 180000,
      spent: 81000,
      status: "active",
      dueDate: "Jan 30, 2026",
      lastUpdate: "1 day ago",
      clientPortalEnabled: true,
      clientPortalToken: "2-client-def456",
      unreadComments: 0,
      description: "Luxury 4-bedroom villa with pool and landscaping",
      startDate: "Oct 1, 2025",
      location: "Hillside Estates",
      contractor: "Premium Homes Inc"
    },
    {
      id: "3",
      name: "Shopping Center Renovation",
      client: "Metro Properties",
      progress: 100,
      budget: 320000,
      spent: 315000,
      status: "completed",
      dueDate: "Nov 1, 2025",
      lastUpdate: "1 week ago",
      clientPortalEnabled: false,
      clientPortalToken: "",
      unreadComments: 0,
      description: "Complete renovation of 50,000 sq ft retail space",
      startDate: "Jun 1, 2025",
      location: "Metro Mall",
      contractor: "Renovation Experts LLC"
    },
    {
      id: "4",
      name: "Industrial Warehouse",
      client: "LogiCorp",
      progress: 30,
      budget: 450000,
      spent: 135000,
      status: "active",
      dueDate: "Mar 15, 2026",
      lastUpdate: "3 hours ago",
      clientPortalEnabled: true,
      clientPortalToken: "4-client-ghi789",
      unreadComments: 1,
      description: "80,000 sq ft distribution center with automated systems",
      startDate: "Nov 1, 2025",
      location: "Industrial Park",
      contractor: "Heavy Build Co"
    },
    {
      id: "5",
      name: "School Extension",
      client: "City Education Board",
      progress: 60,
      budget: 280000,
      spent: 168000,
      status: "active",
      dueDate: "Aug 1, 2026",
      lastUpdate: "5 hours ago",
      clientPortalEnabled: true,
      clientPortalToken: "5-client-jkl012",
      unreadComments: 0,
      description: "New wing addition with 12 classrooms and gymnasium",
      startDate: "Feb 1, 2025",
      location: "Central School District",
      contractor: "Educational Builders"
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active": return "bg-green-500";
      case "completed": return "bg-blue-500";
      case "on-hold": return "bg-yellow-500";
      case "planning": return "bg-purple-500";
      default: return "bg-gray-500";
    }
  };

  const getStatusVariant = (status: string) => {
    switch (status) {
      case "active": return "default";
      case "completed": return "secondary";
      case "on-hold": return "outline";
      default: return "outline";
    }
  };

  const filteredProjects = projects.filter(project => {
    const matchesSearch = project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.client.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === "all" || project.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const getProjectsByStatus = (status: string) => {
    if (status === "all") return filteredProjects;
    return filteredProjects.filter(p => p.status === status);
  };

  const totalBudget = projects.reduce((sum, p) => sum + p.budget, 0);
  const totalSpent = projects.reduce((sum, p) => sum + p.spent, 0);
  const activeProjects = projects.filter(p => p.status === "active").length;
  const completedProjects = projects.filter(p => p.status === "completed").length;

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
          <div>
            <h1 className="text-3xl font-bold mb-2">Projects</h1>
            <p className="text-muted-foreground">
              Manage all your construction projects in one place
            </p>
          </div>
          <Button asChild className="mt-4 md:mt-0">
            <Link to="/project/new">
              <Plus className="mr-2 h-4 w-4" />
              New Project
            </Link>
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Projects</CardTitle>
              <Settings className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{projects.length}</div>
              <p className="text-xs text-muted-foreground">
                {activeProjects} active, {completedProjects} completed
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Budget</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">${(totalBudget / 1000).toFixed(0)}K</div>
              <p className="text-xs text-muted-foreground">
                Across all projects
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Spent</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">${(totalSpent / 1000).toFixed(0)}K</div>
              <p className="text-xs text-muted-foreground">
                {((totalSpent / totalBudget) * 100).toFixed(1)}% of budget
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Progress</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {Math.round(projects.reduce((sum, p) => sum + p.progress, 0) / projects.length)}%
              </div>
              <p className="text-xs text-muted-foreground">
                Across active projects
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filter */}
        <div className="flex flex-col md:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search projects by name, client, or description..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {/* Projects List */}
        <Tabs defaultValue="all" className="space-y-6">
          <TabsList>
            <TabsTrigger value="all">All Projects ({filteredProjects.length})</TabsTrigger>
            <TabsTrigger value="active">Active ({getProjectsByStatus("active").length})</TabsTrigger>
            <TabsTrigger value="completed">Completed ({getProjectsByStatus("completed").length})</TabsTrigger>
            <TabsTrigger value="on-hold">On Hold ({getProjectsByStatus("on-hold").length})</TabsTrigger>
          </TabsList>
          
          <TabsContent value="all" className="space-y-6">
            <div className="grid gap-6">
              {getProjectsByStatus("all").map((project) => (
                <Card key={project.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
                      <div className="flex items-center space-x-3">
                        <div className={`w-3 h-3 rounded-full ${getStatusColor(project.status)}`}></div>
                        <div>
                          <CardTitle className="text-lg">{project.name}</CardTitle>
                          <CardDescription>{project.description}</CardDescription>
                          <div className="flex items-center space-x-4 text-sm text-muted-foreground mt-1">
                            <span>Client: {project.client}</span>
                            <span>•</span>
                            <span>Location: {project.location}</span>
                          </div>
                        </div>
                      </div>
                      <div className="flex flex-wrap gap-2 mt-4 md:mt-0">
                        <Badge variant={getStatusVariant(project.status)}>
                          {project.status}
                        </Badge>
                        {project.clientPortalEnabled && (
                          <Badge className="bg-blue-500 text-white">
                            <Share2 className="mr-1 h-3 w-3" />
                            Portal Active
                          </Badge>
                        )}
                        {project.unreadComments > 0 && (
                          <Badge className="bg-red-500 text-white">
                            <MessageCircle className="mr-1 h-3 w-3" />
                            {project.unreadComments} new
                          </Badge>
                        )}
                        <Button variant="outline" size="sm" asChild>
                          <Link to={`/project/${project.id}`}>
                            <Search className="mr-2 h-4 w-4" />
                            View Details
                          </Link>
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div>
                        <div className="flex justify-between items-center mb-2">
                          <span className="text-sm font-medium">Progress</span>
                          <span className="text-sm text-muted-foreground">{project.progress}%</span>
                        </div>
                        <Progress value={project.progress} className="mb-2" />
                        <p className="text-xs text-muted-foreground">
                          Due: {project.dueDate}
                        </p>
                      </div>
                      
                      <div>
                        <div className="flex justify-between items-center mb-2">
                          <span className="text-sm font-medium">Budget</span>
                          <span className="text-sm text-muted-foreground">
                            ${(project.budget / 1000).toFixed(0)}K
                          </span>
                        </div>
                        <Progress 
                          value={(project.spent / project.budget) * 100} 
                          className="mb-2"
                        />
                        <p className="text-xs text-muted-foreground">
                          Spent: ${(project.spent / 1000).toFixed(0)}K ({((project.spent / project.budget) * 100).toFixed(1)}%)
                        </p>
                      </div>
                      
                      <div>
                        <div className="text-sm font-medium mb-2">Project Details</div>
                        <div className="space-y-1 text-xs text-muted-foreground">
                          <p>Started: {project.startDate}</p>
                          <p>Contractor: {project.contractor}</p>
                          <p>Last Update: {project.lastUpdate}</p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
          
          <TabsContent value="active" className="space-y-6">
            <div className="grid gap-6">
              {getProjectsByStatus("active").map((project) => (
                <Card key={project.id} className="hover:shadow-lg transition-shadow">
                  {/* Same card content as above */}
                  <CardHeader>
                    <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
                      <div className="flex items-center space-x-3">
                        <div className={`w-3 h-3 rounded-full ${getStatusColor(project.status)}`}></div>
                        <div>
                          <CardTitle className="text-lg">{project.name}</CardTitle>
                          <CardDescription>{project.description}</CardDescription>
                        </div>
                      </div>
                      <Button variant="outline" size="sm" asChild>
                        <Link to={`/project/${project.id}`}>
                          <Search className="mr-2 h-4 w-4" />
                          View Details
                        </Link>
                      </Button>
                    </div>
                  </CardHeader>
                </Card>
              ))}
            </div>
          </TabsContent>
          
          <TabsContent value="completed" className="space-y-6">
            <div className="grid gap-6">
              {getProjectsByStatus("completed").map((project) => (
                <Card key={project.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
                      <div className="flex items-center space-x-3">
                        <div className={`w-3 h-3 rounded-full ${getStatusColor(project.status)}`}></div>
                        <div>
                          <CardTitle className="text-lg">{project.name}</CardTitle>
                          <CardDescription>{project.description}</CardDescription>
                        </div>
                      </div>
                      <Button variant="outline" size="sm" asChild>
                        <Link to={`/project/${project.id}`}>
                          <Search className="mr-2 h-4 w-4" />
                          View Details
                        </Link>
                      </Button>
                    </div>
                  </CardHeader>
                </Card>
              ))}
            </div>
          </TabsContent>
          
          <TabsContent value="on-hold" className="space-y-6">
            <div className="grid gap-6">
              {getProjectsByStatus("on-hold").map((project) => (
                <Card key={project.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
                      <div className="flex items-center space-x-3">
                        <div className={`w-3 h-3 rounded-full ${getStatusColor(project.status)}`}></div>
                        <div>
                          <CardTitle className="text-lg">{project.name}</CardTitle>
                          <CardDescription>{project.description}</CardDescription>
                        </div>
                      </div>
                      <Button variant="outline" size="sm" asChild>
                        <Link to={`/project/${project.id}`}>
                          <Search className="mr-2 h-4 w-4" />
                          View Details
                        </Link>
                      </Button>
                    </div>
                  </CardHeader>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
};

export default Projects;
