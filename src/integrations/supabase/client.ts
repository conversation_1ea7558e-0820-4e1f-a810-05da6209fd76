// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://rrskyazbvxixrqbphdqt.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJyc2t5YXpidnhpeHJxYnBoZHF0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE4NzMyOTgsImV4cCI6MjA2NzQ0OTI5OH0.UMzYED_oyG8K4HNtLdIGjHKlGeqcY1BKzHIU3WYwh1E";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});