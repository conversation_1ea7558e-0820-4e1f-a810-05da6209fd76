export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      comments: {
        Row: {
          id: string
          content: string
          project_id: string | null
          task_id: string | null
          contract_id: string | null
          quotation_id: string | null
          author_id: string | null
          author_name: string | null
          author_email: string | null
          is_client_comment: boolean
          priority: string
          is_read: boolean
          parent_comment_id: string | null
          attachments: string[] | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          content: string
          project_id?: string | null
          task_id?: string | null
          contract_id?: string | null
          quotation_id?: string | null
          author_id?: string | null
          author_name?: string | null
          author_email?: string | null
          is_client_comment?: boolean
          priority?: string
          is_read?: boolean
          parent_comment_id?: string | null
          attachments?: string[] | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          content?: string
          project_id?: string | null
          task_id?: string | null
          contract_id?: string | null
          quotation_id?: string | null
          author_id?: string | null
          author_name?: string | null
          author_email?: string | null
          is_client_comment?: boolean
          priority?: string
          is_read?: boolean
          parent_comment_id?: string | null
          attachments?: string[] | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "comments_author_id_fkey"
            columns: ["author_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "comments_contract_id_fkey"
            columns: ["contract_id"]
            isOneToOne: false
            referencedRelation: "contracts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "comments_parent_comment_id_fkey"
            columns: ["parent_comment_id"]
            isOneToOne: false
            referencedRelation: "comments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "comments_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "projects"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "comments_quotation_id_fkey"
            columns: ["quotation_id"]
            isOneToOne: false
            referencedRelation: "quotations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "comments_task_id_fkey"
            columns: ["task_id"]
            isOneToOne: false
            referencedRelation: "tasks"
            referencedColumns: ["id"]
          }
        ]
      }
      contracts: {
        Row: {
          id: string
          title: string
          client_name: string
          client_email: string
          client_phone: string | null
          project_type: string | null
          project_address: string | null
          start_date: string | null
          end_date: string | null
          total_amount: number | null
          description: string | null
          scope: string | null
          terms: string | null
          payment_schedule: string | null
          materials: string | null
          labor_details: string | null
          warranties: string | null
          status: string
          shareable_link: string | null
          primary_color: string
          background_color: string
          text_color: string
          company_name: string | null
          company_logo: string | null
          project_id: string | null
          created_by: string | null
          signed_at: string | null
          signed_by_client: string | null
          client_signature: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title: string
          client_name: string
          client_email: string
          client_phone?: string | null
          project_type?: string | null
          project_address?: string | null
          start_date?: string | null
          end_date?: string | null
          total_amount?: number | null
          description?: string | null
          scope?: string | null
          terms?: string | null
          payment_schedule?: string | null
          materials?: string | null
          labor_details?: string | null
          warranties?: string | null
          status?: string
          shareable_link?: string | null
          primary_color?: string
          background_color?: string
          text_color?: string
          company_name?: string | null
          company_logo?: string | null
          project_id?: string | null
          created_by?: string | null
          signed_at?: string | null
          signed_by_client?: string | null
          client_signature?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          client_name?: string
          client_email?: string
          client_phone?: string | null
          project_type?: string | null
          project_address?: string | null
          start_date?: string | null
          end_date?: string | null
          total_amount?: number | null
          description?: string | null
          scope?: string | null
          terms?: string | null
          payment_schedule?: string | null
          materials?: string | null
          labor_details?: string | null
          warranties?: string | null
          status?: string
          shareable_link?: string | null
          primary_color?: string
          background_color?: string
          text_color?: string
          company_name?: string | null
          company_logo?: string | null
          project_id?: string | null
          created_by?: string | null
          signed_at?: string | null
          signed_by_client?: string | null
          client_signature?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "contracts_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "contracts_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "projects"
            referencedColumns: ["id"]
          }
        ]
      }
      profiles: {
        Row: {
          id: string
          email: string
          first_name: string | null
          last_name: string | null
          company: string | null
          role: string
          phone: string | null
          avatar_url: string | null
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          first_name?: string | null
          last_name?: string | null
          company?: string | null
          role?: string
          phone?: string | null
          avatar_url?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          first_name?: string | null
          last_name?: string | null
          company?: string | null
          role?: string
          phone?: string | null
          avatar_url?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      projects: {
        Row: {
          id: string
          name: string
          description: string | null
          status: string
          progress: number
          start_date: string | null
          end_date: string | null
          budget: number
          spent: number
          location: string | null
          project_type: string | null
          priority: string
          client_id: string | null
          contractor_id: string | null
          client_portal_enabled: boolean
          client_portal_token: string | null
          created_by: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          status?: string
          progress?: number
          start_date?: string | null
          end_date?: string | null
          budget?: number
          spent?: number
          location?: string | null
          project_type?: string | null
          priority?: string
          client_id?: string | null
          contractor_id?: string | null
          client_portal_enabled?: boolean
          client_portal_token?: string | null
          created_by?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          status?: string
          progress?: number
          start_date?: string | null
          end_date?: string | null
          budget?: number
          spent?: number
          location?: string | null
          project_type?: string | null
          priority?: string
          client_id?: string | null
          contractor_id?: string | null
          client_portal_enabled?: boolean
          client_portal_token?: string | null
          created_by?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "projects_client_id_fkey"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "projects_contractor_id_fkey"
            columns: ["contractor_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "projects_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          }
        ]
      }
      project_phases: {
        Row: {
          id: string
          project_id: string
          name: string
          description: string | null
          start_date: string | null
          end_date: string | null
          actual_start_date: string | null
          actual_end_date: string | null
          progress: number
          status: string
          budget: number
          spent: number
          order_index: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          project_id: string
          name: string
          description?: string | null
          start_date?: string | null
          end_date?: string | null
          actual_start_date?: string | null
          actual_end_date?: string | null
          progress?: number
          status?: string
          budget?: number
          spent?: number
          order_index?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          project_id?: string
          name?: string
          description?: string | null
          start_date?: string | null
          end_date?: string | null
          actual_start_date?: string | null
          actual_end_date?: string | null
          progress?: number
          status?: string
          budget?: number
          spent?: number
          order_index?: number
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "project_phases_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "projects"
            referencedColumns: ["id"]
          }
        ]
      }
      project_team_members: {
        Row: {
          id: string
          project_id: string
          user_id: string
          role: string
          permissions: string[]
          hourly_rate: number | null
          department: string | null
          is_active: boolean
          joined_date: string
          created_at: string
        }
        Insert: {
          id?: string
          project_id: string
          user_id: string
          role: string
          permissions?: string[]
          hourly_rate?: number | null
          department?: string | null
          is_active?: boolean
          joined_date?: string
          created_at?: string
        }
        Update: {
          id?: string
          project_id?: string
          user_id?: string
          role?: string
          permissions?: string[]
          hourly_rate?: number | null
          department?: string | null
          is_active?: boolean
          joined_date?: string
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "project_team_members_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "projects"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "project_team_members_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          }
        ]
      }
      quotations: {
        Row: {
          id: string
          title: string
          client_name: string
          client_email: string
          client_phone: string | null
          project_type: string | null
          project_address: string | null
          valid_until: string | null
          subtotal: number
          tax_rate: number
          tax_amount: number
          total_amount: number
          description: string | null
          terms: string | null
          notes: string | null
          status: string
          shareable_link: string | null
          primary_color: string
          background_color: string
          text_color: string
          company_name: string | null
          company_logo: string | null
          project_id: string | null
          created_by: string | null
          accepted_at: string | null
          accepted_by_client: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title: string
          client_name: string
          client_email: string
          client_phone?: string | null
          project_type?: string | null
          project_address?: string | null
          valid_until?: string | null
          subtotal?: number
          tax_rate?: number
          tax_amount?: number
          total_amount?: number
          description?: string | null
          terms?: string | null
          notes?: string | null
          status?: string
          shareable_link?: string | null
          primary_color?: string
          background_color?: string
          text_color?: string
          company_name?: string | null
          company_logo?: string | null
          project_id?: string | null
          created_by?: string | null
          accepted_at?: string | null
          accepted_by_client?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          client_name?: string
          client_email?: string
          client_phone?: string | null
          project_type?: string | null
          project_address?: string | null
          valid_until?: string | null
          subtotal?: number
          tax_rate?: number
          tax_amount?: number
          total_amount?: number
          description?: string | null
          terms?: string | null
          notes?: string | null
          status?: string
          shareable_link?: string | null
          primary_color?: string
          background_color?: string
          text_color?: string
          company_name?: string | null
          company_logo?: string | null
          project_id?: string | null
          created_by?: string | null
          accepted_at?: string | null
          accepted_by_client?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "quotations_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "quotations_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "projects"
            referencedColumns: ["id"]
          }
        ]
      }
      quotation_line_items: {
        Row: {
          id: string
          quotation_id: string
          description: string
          quantity: number
          unit_price: number
          total: number | null
          order_index: number
          created_at: string
        }
        Insert: {
          id?: string
          quotation_id: string
          description: string
          quantity: number
          unit_price: number
          order_index?: number
          created_at?: string
        }
        Update: {
          id?: string
          quotation_id?: string
          description?: string
          quantity?: number
          unit_price?: number
          order_index?: number
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "quotation_line_items_quotation_id_fkey"
            columns: ["quotation_id"]
            isOneToOne: false
            referencedRelation: "quotations"
            referencedColumns: ["id"]
          }
        ]
      }
      tasks: {
        Row: {
          id: string
          title: string
          description: string | null
          project_id: string
          phase_id: string | null
          assignee_id: string | null
          location: string | null
          scheduled_date: string | null
          start_time: string | null
          end_time: string | null
          priority: string
          task_type: string | null
          status: string
          progress: number
          estimated_hours: number | null
          actual_hours: number | null
          dependencies: string[] | null
          created_by: string | null
          completed_at: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title: string
          description?: string | null
          project_id: string
          phase_id?: string | null
          assignee_id?: string | null
          location?: string | null
          scheduled_date?: string | null
          start_time?: string | null
          end_time?: string | null
          priority?: string
          task_type?: string | null
          status?: string
          progress?: number
          estimated_hours?: number | null
          actual_hours?: number | null
          dependencies?: string[] | null
          created_by?: string | null
          completed_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          description?: string | null
          project_id?: string
          phase_id?: string | null
          assignee_id?: string | null
          location?: string | null
          scheduled_date?: string | null
          start_time?: string | null
          end_time?: string | null
          priority?: string
          task_type?: string | null
          status?: string
          progress?: number
          estimated_hours?: number | null
          actual_hours?: number | null
          dependencies?: string[] | null
          created_by?: string | null
          completed_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "tasks_assignee_id_fkey"
            columns: ["assignee_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "tasks_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "tasks_phase_id_fkey"
            columns: ["phase_id"]
            isOneToOne: false
            referencedRelation: "project_phases"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "tasks_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "projects"
            referencedColumns: ["id"]
          }
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      get_project_stats: {
        Args: {
          user_id: string
        }
        Returns: Json
      }
      get_unread_comments_count: {
        Args: {
          user_id: string
        }
        Returns: number
      }
      mark_comments_as_read: {
        Args: {
          comment_ids: string[]
        }
        Returns: undefined
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type PublicSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  PublicTableNameOrOptions extends
    | keyof (PublicSchema["Tables"] & PublicSchema["Views"])
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
        Database[PublicTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
      Database[PublicTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : PublicTableNameOrOptions extends keyof (PublicSchema["Tables"] &
        PublicSchema["Views"])
    ? (PublicSchema["Tables"] &
        PublicSchema["Views"])[PublicTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  PublicTableNameOrOptions extends
    | keyof PublicSchema["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema["Tables"]
    ? PublicSchema["Tables"][PublicTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  PublicTableNameOrOptions extends
    | keyof PublicSchema["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema["Tables"]
    ? PublicSchema["Tables"][PublicTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  PublicEnumNameOrOptions extends
    | keyof PublicSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends PublicEnumNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = PublicEnumNameOrOptions extends { schema: keyof Database }
  ? Database[PublicEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : PublicEnumNameOrOptions extends keyof PublicSchema["Enums"]
    ? PublicSchema["Enums"][PublicEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof PublicSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof PublicSchema["CompositeTypes"]
    ? PublicSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never