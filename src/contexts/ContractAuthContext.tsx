
import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { User, Session } from '@supabase/supabase-js';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from './AuthContext';
import { Database } from '@/integrations/supabase/types';

type Contract = Database['public']['Tables']['contracts']['Row'];
type ContractInsert = Database['public']['Tables']['contracts']['Insert'];
type ContractUpdate = Database['public']['Tables']['contracts']['Update'];

interface ContractAuthContextType {
  contracts: Contract[];
  isLoading: boolean;
  createContract: (contractData: ContractInsert) => Promise<Contract | null>;
  updateContract: (id: string, updates: ContractUpdate) => Promise<boolean>;
  deleteContract: (id: string) => Promise<boolean>;
  getContract: (id: string) => Contract | undefined;
  resendContract: (id: string) => Promise<boolean>;
  duplicateContract: (id: string) => Promise<Contract | null>;
  refreshContracts: () => Promise<void>;
}

const ContractAuthContext = createContext<ContractAuthContextType | undefined>(undefined);

export const useContractAuth = () => {
  const context = useContext(ContractAuthContext);
  if (context === undefined) {
    throw new Error('useContractAuth must be used within a ContractAuthProvider');
  }
  return context;
};

interface ContractAuthProviderProps {
  children: ReactNode;
}

export const ContractAuthProvider: React.FC<ContractAuthProviderProps> = ({ children }) => {
  const { user, isAuthenticated } = useAuth();
  const [contracts, setContracts] = useState<Contract[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const refreshContracts = async () => {
    if (!user) {
      setContracts([]);
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      const { data, error } = await supabase
        .from('contracts')
        .select('*')
        .eq('created_by', user.id)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching contracts:', error);
        setContracts([]);
      } else {
        setContracts(data || []);
      }
    } catch (error) {
      console.error('Error fetching contracts:', error);
      setContracts([]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    refreshContracts();
  }, [user]);

  const createContract = async (contractData: ContractInsert): Promise<Contract | null> => {
    if (!user) return null;

    try {
      const { data, error } = await supabase
        .from('contracts')
        .insert({
          ...contractData,
          created_by: user.id,
          status: contractData.status || 'draft'
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating contract:', error);
        return null;
      }

      await refreshContracts();
      return data;
    } catch (error) {
      console.error('Error creating contract:', error);
      return null;
    }
  };

  const updateContract = async (id: string, updates: ContractUpdate): Promise<boolean> => {
    try {
      const { error } = await supabase
        .from('contracts')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', id);

      if (error) {
        console.error('Error updating contract:', error);
        return false;
      }

      await refreshContracts();
      return true;
    } catch (error) {
      console.error('Error updating contract:', error);
      return false;
    }
  };

  const deleteContract = async (id: string): Promise<boolean> => {
    try {
      const { error } = await supabase
        .from('contracts')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Error deleting contract:', error);
        return false;
      }

      await refreshContracts();
      return true;
    } catch (error) {
      console.error('Error deleting contract:', error);
      return false;
    }
  };

  const getContract = (id: string): Contract | undefined => {
    return contracts.find(contract => contract.id === id);
  };

  const resendContract = async (id: string): Promise<boolean> => {
    return await updateContract(id, {
      status: 'sent'
    });
  };

  const duplicateContract = async (id: string): Promise<Contract | null> => {
    const originalContract = getContract(id);
    if (!originalContract) return null;

    const { id: _, created_at, updated_at, ...contractData } = originalContract;

    return await createContract({
      ...contractData,
      title: `${originalContract.title} (Copy)`,
      client_name: '',
      client_email: ''
    });
  };

  const value: ContractAuthContextType = {
    contracts,
    isLoading,
    createContract,
    updateContract,
    deleteContract,
    getContract,
    resendContract,
    duplicateContract,
    refreshContracts
  };

  return (
    <ContractAuthContext.Provider value={value}>
      {children}
    </ContractAuthContext.Provider>
  );
};
