
import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { User, Session } from '@supabase/supabase-js';
import { supabase } from '@/integrations/supabase/client';

interface ContractUser {
  id: string;
  email: string;
  name: string;
  company?: string;
  phone?: string;
  createdAt: string;
}

interface Contract {
  id: string;
  title: string;
  type: string;
  status: 'draft' | 'sent' | 'signed' | 'expired' | 'cancelled';
  clientName: string;
  clientEmail: string;
  totalCost: number;
  createdDate: string;
  lastModified: string;
  signedDate?: string;
  expiryDate?: string;
  template: string;
  projectScope: string;
  startDate: string;
  completionDate: string;
  materialCost: number;
  laborCost: number;
  depositAmount: number;
  paymentTerms: string;
  warrantyPeriod: string;
  customClauses: string;
  deliverables: string;
  milestones: string;
  user_id?: string;
}

interface ContractAuthContextType {
  user: ContractUser | null;
  contracts: Contract[];
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<boolean>;
  register: (email: string, password: string, name: string, company?: string) => Promise<boolean>;
  logout: () => void;
  createContract: (contractData: Omit<Contract, 'id' | 'createdDate' | 'lastModified' | 'status' | 'user_id'>) => Contract;
  updateContract: (id: string, updates: Partial<Contract>) => void;
  deleteContract: (id: string) => void;
  getContract: (id: string) => Contract | undefined;
  resendContract: (id: string) => Promise<boolean>;
  duplicateContract: (id: string) => Contract | null;
}

const ContractAuthContext = createContext<ContractAuthContextType | undefined>(undefined);

export const useContractAuth = () => {
  const context = useContext(ContractAuthContext);
  if (context === undefined) {
    throw new Error('useContractAuth must be used within a ContractAuthProvider');
  }
  return context;
};

interface ContractAuthProviderProps {
  children: ReactNode;
}

export const ContractAuthProvider: React.FC<ContractAuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<ContractUser | null>(null);
  const [contracts, setContracts] = useState<Contract[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Mock data for demonstration
  const mockContracts: Contract[] = [
    {
      id: "CNT001",
      title: "Downtown Office Complex - Main Contract",
      type: "fixed-price",
      status: "signed",
      clientName: "Sterling Corp",
      clientEmail: "<EMAIL>",
      totalCost: 250000,
      createdDate: "2024-11-01",
      lastModified: "2024-11-05",
      signedDate: "2024-11-05",
      template: "fixed-price",
      projectScope: "Complete office complex construction including electrical, plumbing, and HVAC systems",
      startDate: "2024-11-15",
      completionDate: "2025-05-15",
      materialCost: 150000,
      laborCost: 100000,
      depositAmount: 50000,
      paymentTerms: "50% deposit, 25% at 50% completion, 25% on completion",
      warrantyPeriod: "2-years",
      customClauses: "Standard warranty terms apply",
      deliverables: "Fully functional office space with all systems operational",
      milestones: "Foundation: 30 days, Structure: 90 days, Finishing: 180 days"
    },
    {
      id: "CNT002",
      title: "Residential Kitchen Renovation",
      type: "cost-plus",
      status: "sent",
      clientName: "John & Mary Smith",
      clientEmail: "<EMAIL>",
      totalCost: 45000,
      createdDate: "2024-11-10",
      lastModified: "2024-11-12",
      template: "renovation-contract",
      projectScope: "Complete kitchen renovation including cabinets, countertops, appliances, and flooring",
      startDate: "2024-12-01",
      completionDate: "2025-01-15",
      materialCost: 30000,
      laborCost: 15000,
      depositAmount: 15000,
      paymentTerms: "33% deposit, 33% at midpoint, 34% on completion",
      warrantyPeriod: "1-year",
      customClauses: "Client to provide appliances",
      deliverables: "Fully renovated kitchen ready for use",
      milestones: "Demo: 3 days, Installation: 30 days, Finishing: 45 days"
    }
  ];

  useEffect(() => {
    // For now, using localStorage for mock authentication
    // In production, this would use Supabase auth
    const savedUser = localStorage.getItem('contractUser');
    const savedContracts = localStorage.getItem('contractUserContracts');
    
    if (savedUser) {
      setUser(JSON.parse(savedUser));
      setContracts(savedContracts ? JSON.parse(savedContracts) : mockContracts);
    }
    setIsLoading(false);
  }, []);

  const login = async (email: string, password: string): Promise<boolean> => {
    setIsLoading(true);
    
    // Mock authentication - in production, use Supabase
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    if (email && password) {
      const mockUser: ContractUser = {
        id: 'contract_user_1',
        email,
        name: email.split('@')[0],
        company: 'Professional Contracting LLC',
        createdAt: new Date().toISOString()
      };
      
      setUser(mockUser);
      setContracts(mockContracts);
      localStorage.setItem('contractUser', JSON.stringify(mockUser));
      localStorage.setItem('contractUserContracts', JSON.stringify(mockContracts));
      setIsLoading(false);
      return true;
    }
    
    setIsLoading(false);
    return false;
  };

  const register = async (email: string, password: string, name: string, company?: string): Promise<boolean> => {
    setIsLoading(true);
    
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    if (email && password && name) {
      const newUser: ContractUser = {
        id: `contract_user_${Date.now()}`,
        email,
        name,
        company,
        createdAt: new Date().toISOString()
      };
      
      setUser(newUser);
      setContracts([]);
      localStorage.setItem('contractUser', JSON.stringify(newUser));
      localStorage.setItem('contractUserContracts', JSON.stringify([]));
      setIsLoading(false);
      return true;
    }
    
    setIsLoading(false);
    return false;
  };

  const logout = () => {
    setUser(null);
    setContracts([]);
    localStorage.removeItem('contractUser');
    localStorage.removeItem('contractUserContracts');
  };

  const createContract = (contractData: Omit<Contract, 'id' | 'createdDate' | 'lastModified' | 'status' | 'user_id'>): Contract => {
    const newContract: Contract = {
      ...contractData,
      id: `CNT${Date.now()}`,
      status: 'draft',
      createdDate: new Date().toISOString().split('T')[0],
      lastModified: new Date().toISOString().split('T')[0],
      user_id: user?.id
    };
    
    const updatedContracts = [...contracts, newContract];
    setContracts(updatedContracts);
    localStorage.setItem('contractUserContracts', JSON.stringify(updatedContracts));
    
    return newContract;
  };

  const updateContract = (id: string, updates: Partial<Contract>) => {
    const updatedContracts = contracts.map(contract => 
      contract.id === id 
        ? { ...contract, ...updates, lastModified: new Date().toISOString().split('T')[0] }
        : contract
    );
    setContracts(updatedContracts);
    localStorage.setItem('contractUserContracts', JSON.stringify(updatedContracts));
  };

  const deleteContract = (id: string) => {
    const updatedContracts = contracts.filter(contract => contract.id !== id);
    setContracts(updatedContracts);
    localStorage.setItem('contractUserContracts', JSON.stringify(updatedContracts));
  };

  const getContract = (id: string): Contract | undefined => {
    return contracts.find(contract => contract.id === id);
  };

  const resendContract = async (id: string): Promise<boolean> => {
    await new Promise(resolve => setTimeout(resolve, 500));
    
    updateContract(id, { 
      status: 'sent',
      lastModified: new Date().toISOString().split('T')[0]
    });
    
    return true;
  };

  const duplicateContract = (id: string): Contract | null => {
    const originalContract = getContract(id);
    if (!originalContract) return null;
    
    const duplicatedContract = createContract({
      ...originalContract,
      title: `${originalContract.title} (Copy)`,
      clientName: '',
      clientEmail: ''
    });
    
    return duplicatedContract;
  };

  const value: ContractAuthContextType = {
    user,
    contracts,
    isAuthenticated: !!user,
    isLoading,
    login,
    register,
    logout,
    createContract,
    updateContract,
    deleteContract,
    getContract,
    resendContract,
    duplicateContract
  };

  return (
    <ContractAuthContext.Provider value={value}>
      {children}
    </ContractAuthContext.Provider>
  );
};
