
import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { User, Session } from '@supabase/supabase-js';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from './AuthContext';
import { Database } from '@/integrations/supabase/types';

type Quotation = Database['public']['Tables']['quotations']['Row'];
type QuotationInsert = Database['public']['Tables']['quotations']['Insert'];
type QuotationUpdate = Database['public']['Tables']['quotations']['Update'];
type QuotationLineItem = Database['public']['Tables']['quotation_line_items']['Row'];

interface QuotationAuthContextType {
  quotations: Quotation[];
  isLoading: boolean;
  createQuotation: (quotationData: QuotationInsert) => Promise<Quotation | null>;
  updateQuotation: (id: string, updates: QuotationUpdate) => Promise<boolean>;
  deleteQuotation: (id: string) => Promise<boolean>;
  getQuotation: (id: string) => Quotation | undefined;
  resendQuotation: (id: string) => Promise<boolean>;
  duplicateQuotation: (id: string) => Promise<Quotation | null>;
  refreshQuotations: () => Promise<void>;
}

const QuotationAuthContext = createContext<QuotationAuthContextType | undefined>(undefined);

export const useQuotationAuth = () => {
  const context = useContext(QuotationAuthContext);
  if (context === undefined) {
    throw new Error('useQuotationAuth must be used within a QuotationAuthProvider');
  }
  return context;
};

interface QuotationAuthProviderProps {
  children: ReactNode;
}

export const QuotationAuthProvider: React.FC<QuotationAuthProviderProps> = ({ children }) => {
  const { user, isAuthenticated } = useAuth();
  const [quotations, setQuotations] = useState<Quotation[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const refreshQuotations = async () => {
    if (!user) {
      setQuotations([]);
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      const { data, error } = await supabase
        .from('quotations')
        .select('*')
        .eq('created_by', user.id)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching quotations:', error);
        setQuotations([]);
      } else {
        setQuotations(data || []);
      }
    } catch (error) {
      console.error('Error fetching quotations:', error);
      setQuotations([]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    refreshQuotations();
  }, [user]);

  const createQuotation = async (quotationData: QuotationInsert): Promise<Quotation | null> => {
    if (!user) return null;

    try {
      const { data, error } = await supabase
        .from('quotations')
        .insert({
          ...quotationData,
          created_by: user.id,
          status: quotationData.status || 'draft'
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating quotation:', error);
        return null;
      }

      await refreshQuotations();
      return data;
    } catch (error) {
      console.error('Error creating quotation:', error);
      return null;
    }
  };

  const updateQuotation = async (id: string, updates: QuotationUpdate): Promise<boolean> => {
    try {
      const { error } = await supabase
        .from('quotations')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', id);

      if (error) {
        console.error('Error updating quotation:', error);
        return false;
      }

      await refreshQuotations();
      return true;
    } catch (error) {
      console.error('Error updating quotation:', error);
      return false;
    }
  };

  const deleteQuotation = async (id: string): Promise<boolean> => {
    try {
      const { error } = await supabase
        .from('quotations')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Error deleting quotation:', error);
        return false;
      }

      await refreshQuotations();
      return true;
    } catch (error) {
      console.error('Error deleting quotation:', error);
      return false;
    }
  };

  const getQuotation = (id: string): Quotation | undefined => {
    return quotations.find(quotation => quotation.id === id);
  };

  const resendQuotation = async (id: string): Promise<boolean> => {
    return await updateQuotation(id, {
      status: 'sent'
    });
  };

  const duplicateQuotation = async (id: string): Promise<Quotation | null> => {
    const originalQuotation = getQuotation(id);
    if (!originalQuotation) return null;

    const { id: _, created_at, updated_at, ...quotationData } = originalQuotation;

    return await createQuotation({
      ...quotationData,
      title: `${originalQuotation.title} (Copy)`,
      client_name: '',
      client_email: ''
    });
  };

  const value: QuotationAuthContextType = {
    quotations,
    isLoading,
    createQuotation,
    updateQuotation,
    deleteQuotation,
    getQuotation,
    resendQuotation,
    duplicateQuotation,
    refreshQuotations
  };

  return (
    <QuotationAuthContext.Provider value={value}>
      {children}
    </QuotationAuthContext.Provider>
  );
};
