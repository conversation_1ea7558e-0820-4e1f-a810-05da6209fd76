
import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { User, Session } from '@supabase/supabase-js';
import { supabase } from '@/integrations/supabase/client';

interface QuotationUser {
  id: string;
  email: string;
  name: string;
  company?: string;
  phone?: string;
  createdAt: string;
}

interface Quotation {
  id: string;
  title: string;
  type: string;
  status: 'draft' | 'sent' | 'approved' | 'rejected' | 'expired';
  clientName: string;
  clientEmail: string;
  totalCost: number;
  createdDate: string;
  lastModified: string;
  approvedDate?: string;
  expiryDate?: string;
  template: string;
  projectScope: string;
  items: Array<{
    id: string;
    description: string;
    quantity: number;
    unitPrice: number;
    total: number;
  }>;
  notes: string;
  terms: string;
  validUntil: string;
  user_id?: string;
}

interface QuotationAuthContextType {
  user: QuotationUser | null;
  quotations: Quotation[];
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<boolean>;
  register: (email: string, password: string, name: string, company?: string) => Promise<boolean>;
  logout: () => void;
  createQuotation: (quotationData: Omit<Quotation, 'id' | 'createdDate' | 'lastModified' | 'status' | 'user_id'>) => Quotation;
  updateQuotation: (id: string, updates: Partial<Quotation>) => void;
  deleteQuotation: (id: string) => void;
  getQuotation: (id: string) => Quotation | undefined;
  resendQuotation: (id: string) => Promise<boolean>;
  duplicateQuotation: (id: string) => Quotation | null;
}

const QuotationAuthContext = createContext<QuotationAuthContextType | undefined>(undefined);

export const useQuotationAuth = () => {
  const context = useContext(QuotationAuthContext);
  if (context === undefined) {
    throw new Error('useQuotationAuth must be used within a QuotationAuthProvider');
  }
  return context;
};

interface QuotationAuthProviderProps {
  children: ReactNode;
}

export const QuotationAuthProvider: React.FC<QuotationAuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<QuotationUser | null>(null);
  const [quotations, setQuotations] = useState<Quotation[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Mock data for demonstration
  const mockQuotations: Quotation[] = [
    {
      id: "QUO001",
      title: "Bathroom Renovation Quote",
      type: "renovation",
      status: "approved",
      clientName: "Alice Johnson",
      clientEmail: "<EMAIL>",
      totalCost: 15000,
      createdDate: "2024-11-01",
      lastModified: "2024-11-03",
      approvedDate: "2024-11-03",
      template: "renovation-quote",
      projectScope: "Complete bathroom renovation including tiles, fixtures, and plumbing",
      items: [
        { id: "1", description: "Tiles and materials", quantity: 1, unitPrice: 5000, total: 5000 },
        { id: "2", description: "Labor costs", quantity: 40, unitPrice: 150, total: 6000 },
        { id: "3", description: "Fixtures and fittings", quantity: 1, unitPrice: 4000, total: 4000 }
      ],
      notes: "Includes 1-year warranty on workmanship",
      terms: "50% deposit required, balance on completion",
      validUntil: "2024-12-01"
    }
  ];

  useEffect(() => {
    // For now, using localStorage for mock authentication
    const savedUser = localStorage.getItem('quotationUser');
    const savedQuotations = localStorage.getItem('quotationUserQuotations');
    
    if (savedUser) {
      setUser(JSON.parse(savedUser));
      setQuotations(savedQuotations ? JSON.parse(savedQuotations) : mockQuotations);
    }
    setIsLoading(false);
  }, []);

  const login = async (email: string, password: string): Promise<boolean> => {
    setIsLoading(true);
    
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    if (email && password) {
      const mockUser: QuotationUser = {
        id: 'quotation_user_1',
        email,
        name: email.split('@')[0],
        company: 'Professional Estimating LLC',
        createdAt: new Date().toISOString()
      };
      
      setUser(mockUser);
      setQuotations(mockQuotations);
      localStorage.setItem('quotationUser', JSON.stringify(mockUser));
      localStorage.setItem('quotationUserQuotations', JSON.stringify(mockQuotations));
      setIsLoading(false);
      return true;
    }
    
    setIsLoading(false);
    return false;
  };

  const register = async (email: string, password: string, name: string, company?: string): Promise<boolean> => {
    setIsLoading(true);
    
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    if (email && password && name) {
      const newUser: QuotationUser = {
        id: `quotation_user_${Date.now()}`,
        email,
        name,
        company,
        createdAt: new Date().toISOString()
      };
      
      setUser(newUser);
      setQuotations([]);
      localStorage.setItem('quotationUser', JSON.stringify(newUser));
      localStorage.setItem('quotationUserQuotations', JSON.stringify([]));
      setIsLoading(false);
      return true;
    }
    
    setIsLoading(false);
    return false;
  };

  const logout = () => {
    setUser(null);
    setQuotations([]);
    localStorage.removeItem('quotationUser');
    localStorage.removeItem('quotationUserQuotations');
  };

  const createQuotation = (quotationData: Omit<Quotation, 'id' | 'createdDate' | 'lastModified' | 'status' | 'user_id'>): Quotation => {
    const newQuotation: Quotation = {
      ...quotationData,
      id: `QUO${Date.now()}`,
      status: 'draft',
      createdDate: new Date().toISOString().split('T')[0],
      lastModified: new Date().toISOString().split('T')[0],
      user_id: user?.id
    };
    
    const updatedQuotations = [...quotations, newQuotation];
    setQuotations(updatedQuotations);
    localStorage.setItem('quotationUserQuotations', JSON.stringify(updatedQuotations));
    
    return newQuotation;
  };

  const updateQuotation = (id: string, updates: Partial<Quotation>) => {
    const updatedQuotations = quotations.map(quotation => 
      quotation.id === id 
        ? { ...quotation, ...updates, lastModified: new Date().toISOString().split('T')[0] }
        : quotation
    );
    setQuotations(updatedQuotations);
    localStorage.setItem('quotationUserQuotations', JSON.stringify(updatedQuotations));
  };

  const deleteQuotation = (id: string) => {
    const updatedQuotations = quotations.filter(quotation => quotation.id !== id);
    setQuotations(updatedQuotations);
    localStorage.setItem('quotationUserQuotations', JSON.stringify(updatedQuotations));
  };

  const getQuotation = (id: string): Quotation | undefined => {
    return quotations.find(quotation => quotation.id === id);
  };

  const resendQuotation = async (id: string): Promise<boolean> => {
    await new Promise(resolve => setTimeout(resolve, 500));
    
    updateQuotation(id, { 
      status: 'sent',
      lastModified: new Date().toISOString().split('T')[0]
    });
    
    return true;
  };

  const duplicateQuotation = (id: string): Quotation | null => {
    const originalQuotation = getQuotation(id);
    if (!originalQuotation) return null;
    
    const duplicatedQuotation = createQuotation({
      ...originalQuotation,
      title: `${originalQuotation.title} (Copy)`,
      clientName: '',
      clientEmail: ''
    });
    
    return duplicatedQuotation;
  };

  const value: QuotationAuthContextType = {
    user,
    quotations,
    isAuthenticated: !!user,
    isLoading,
    login,
    register,
    logout,
    createQuotation,
    updateQuotation,
    deleteQuotation,
    getQuotation,
    resendQuotation,
    duplicateQuotation
  };

  return (
    <QuotationAuthContext.Provider value={value}>
      {children}
    </QuotationAuthContext.Provider>
  );
};
