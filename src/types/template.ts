
export interface WebTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  type?: string;
  rating?: number;
  downloads?: number;
  features?: string[];
  preview?: string;
  isInteractive?: boolean;
  hasSignature?: boolean;
  mobileOptimized?: boolean;
}

export interface ContractTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  type: string;
  rating: number;
  downloads: number;
  features: string[];
  preview: string;
}

export interface QuotationTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  type: string;
  rating: number;
  downloads: number;
  features: string[];
  preview: string;
}
