
export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  company: string;
  role: 'contractor' | 'client';
  createdAt: string;
}

export interface ProjectPhase {
  id: string;
  name: string;
  description: string;
  startDate: string;
  endDate: string;
  progress: number;
  status: 'not-started' | 'in-progress' | 'completed' | 'delayed';
  budget: number;
  spent: number;
  actualStartDate?: string;
  actualEndDate?: string;
}

export interface TeamMember {
  id: string;
  name: string;
  email: string;
  role: string;
  avatar?: string;
  phone?: string;
  department?: string;
  hourlyRate?: number;
  permissions?: string[];
  isActive: boolean;
  joinedDate: string;
}

export interface Project {
  id: string;
  name: string;
  description: string;
  status: 'planning' | 'active' | 'completed' | 'on-hold';
  progress: number;
  startDate: string;
  endDate: string;
  budget: number;
  spent: number;
  client: {
    name: string;
    email: string;
    company?: string;
  };
  phases: ProjectPhase[];
  team: TeamMember[];
  createdAt: string;
  updatedAt: string;
}

export interface ClientComment {
  id: string;
  content: string;
  author: string;
  authorEmail: string;
  timestamp: string;
  isClientComment: boolean;
  priority: 'low' | 'medium' | 'high';
  isRead: boolean;
  attachments?: string[];
  parentCommentId?: string;
}

export interface ClientCommentResponse {
  id: string;
  content: string;
  author: string;
  timestamp: string;
  parentCommentId: string;
}

export interface ProjectFormData {
  name: string;
  description: string;
  clientName: string;
  clientEmail: string;
  clientCompany?: string;
  startDate: string;
  endDate: string;
  budget: number;
  projectType?: string;
  location?: string;
  priority: 'low' | 'medium' | 'high';
}

// Mock functions for missing exports
export const createClientComment = async (comment: Omit<ClientComment, 'id' | 'timestamp'>): Promise<ClientComment> => {
  return {
    ...comment,
    id: Math.random().toString(36).substr(2, 9),
    timestamp: new Date().toISOString(),
  };
};

export const addCommentResponse = async (response: Omit<ClientCommentResponse, 'id' | 'timestamp'>): Promise<ClientCommentResponse> => {
  return {
    ...response,
    id: Math.random().toString(36).substr(2, 9),
    timestamp: new Date().toISOString(),
  };
};

export const validateProjectFormData = (data: ProjectFormData): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  if (!data.name.trim()) errors.push('Project name is required');
  if (!data.clientName.trim()) errors.push('Client name is required');
  if (!data.clientEmail.trim()) errors.push('Client email is required');
  if (!data.startDate) errors.push('Start date is required');
  if (!data.endDate) errors.push('End date is required');
  if (data.budget <= 0) errors.push('Budget must be greater than 0');

  return {
    isValid: errors.length === 0,
    errors
  };
};

export const convertFormDataToProject = (data: ProjectFormData): Omit<Project, 'id' | 'createdAt' | 'updatedAt'> => {
  return {
    name: data.name,
    description: data.description,
    status: 'planning',
    progress: 0,
    startDate: data.startDate,
    endDate: data.endDate,
    budget: data.budget,
    spent: 0,
    client: {
      name: data.clientName,
      email: data.clientEmail,
      company: data.clientCompany,
    },
    phases: [],
    team: [],
  };
};
