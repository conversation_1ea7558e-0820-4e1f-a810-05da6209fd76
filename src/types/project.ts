
export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  company: string;
  role: 'contractor' | 'client';
  createdAt: string;
}

export interface ProjectPhase {
  id: string;
  name: string;
  description: string;
  startDate: string;
  endDate: string;
  progress: number;
  status: 'not-started' | 'in-progress' | 'completed' | 'delayed';
  budget: number;
  spent: number;
  actualStartDate?: string;
  actualEndDate?: string;
}

export interface TeamMember {
  id: string;
  name: string;
  email: string;
  role: string;
  avatar?: string;
  phone?: string;
  department?: string;
  hourlyRate?: number;
  permissions?: string[];
  isActive: boolean;
  joinedDate: string;
}

export interface Project {
  id: string;
  name: string;
  description: string;
  status: 'planning' | 'active' | 'completed' | 'on-hold';
  progress: number;
  startDate: string;
  endDate: string;
  budget: number;
  spent: number;
  client: {
    name: string;
    email: string;
    company?: string;
  };
  phases: ProjectPhase[];
  team: TeamMember[];
  createdAt: string;
  updatedAt: string;
}
